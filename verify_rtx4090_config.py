#!/usr/bin/env python3
"""
RTX 4090配置验证脚本
验证所有配置文件是否正确修改
"""

import os
import re
from pathlib import Path

def check_file_content(file_path, patterns, description):
    """检查文件内容是否包含指定模式"""
    print(f"\n🔍 检查 {description}: {file_path}")
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        results = []
        for pattern_name, pattern in patterns.items():
            if isinstance(pattern, str):
                found = pattern in content
            else:  # regex pattern
                found = bool(re.search(pattern, content))
            
            status = "✅" if found else "❌"
            print(f"   {status} {pattern_name}")
            results.append(found)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def verify_gpu_faker():
    """验证GPU伪装器配置"""
    patterns = {
        "RTX 4090类名": "apply_rtx4090_fake",
        "RTX 4090 VRAM": "24.0",
        "RTX 4090环境变量": "FAKE_RTX4090_GPU",
        "RTX 4090 GPU名称": "RTX 4090 GPU",
        "大模型池门槛": "vram >= 20",
    }
    
    return check_file_content("gpu_faker.py", patterns, "GPU伪装器")

def verify_complete_faker():
    """验证完整伪装器配置"""
    patterns = {
        "RTX 4090类名": "CompleteRTX4090Faker",
        "RTX 4090 GPU名称": "NVIDIA GeForce RTX 4090",
        "24GB内存": "25769803776",
        "计算能力8.9": "self.minor = 9",
        "128个SM": "self.multi_processor_count = 128",
        "Intel处理器": "Intel(R) Core(TM) i9-13900K",
        "64GB内存": "64 * 1024 * 1024 * 1024",
        "32逻辑核心": "count = 32",
        "16物理核心": "count = 16",
    }
    
    return check_file_content("complete_rtx4090_faker.py", patterns, "完整伪装器")

def verify_launch_script():
    """验证启动脚本配置"""
    patterns = {
        "RTX 4090询问函数": "ask_rtx4090_fake",
        "RTX 4090应用函数": "apply_rtx4090_fake",
        "RTX 4090环境变量": "FAKE_RTX4090_GPU",
        "RTX 4090显示信息": "GeForce RTX 4090",
        "Intel处理器显示": "Intel i9-13900K",
        "64GB内存显示": "64GB DDR5",
        "RTX 4090性能级别": "RTX 4090级别",
    }
    
    return check_file_content("launch_no_model_attack.py", patterns, "启动脚本")

def verify_trainer():
    """验证训练器配置"""
    patterns = {
        "RTX 4090 loss范围": "0.08, 0.20",
        "RTX 4090奖励范围": "0.65, 0.85",
        "RTX 4090训练时间": "12, 30",
        "RTX 4090样本速度": "35, 80",
        "RTX 4090训练时间注释": "RTX 4090级别",
    }
    
    return check_file_content("rl-swarm/rgym_exp/src/trainer.py", patterns, "训练器")

def verify_omega_resolver():
    """验证GPU解析器配置"""
    patterns = {
        "RTX 4090环境变量": "FAKE_RTX4090_GPU",
        "RTX 4090 VRAM": "24.0",
        "RTX 4090门槛": "vram >= 20",
        "RTX 4090补丁标记": "RTX4090_FAKE_PATCH",
    }
    
    return check_file_content("rl-swarm/rgym_exp/src/utils/omega_gpu_resolver.py", patterns, "GPU解析器")

def verify_attack_patch():
    """验证攻击补丁配置"""
    patterns = {
        "RTX 4090最小延迟": "max(2.0, total_delay)",
        "RTX 4090延迟注释": "RTX 4090最小延迟",
    }
    
    return check_file_content("precise_attack_patch.py", patterns, "攻击补丁")

def check_environment_consistency():
    """检查环境变量一致性"""
    print("\n🔍 检查环境变量一致性...")
    
    files_to_check = [
        "gpu_faker.py",
        "complete_rtx4090_faker.py",
        "launch_no_model_attack.py",
        "rl-swarm/rgym_exp/src/utils/omega_gpu_resolver.py"
    ]
    
    rtx4090_count = 0
    a100_count = 0
    
    for file_path in files_to_check:
        if Path(file_path).exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                rtx4090_count += content.count('FAKE_RTX4090_GPU')
                a100_count += content.count('FAKE_A100_GPU')
                
            except Exception as e:
                print(f"⚠️  无法读取 {file_path}: {e}")
    
    print(f"   ✅ RTX 4090环境变量引用: {rtx4090_count} 次")
    print(f"   {'✅' if a100_count == 0 else '⚠️'} A100环境变量引用: {a100_count} 次")
    
    return rtx4090_count > 0 and a100_count == 0

def main():
    """主验证函数"""
    print("🔍 RTX 4090配置完整性验证")
    print("=" * 60)
    
    verification_results = []
    
    # 运行各项验证
    verification_results.append(("GPU伪装器", verify_gpu_faker()))
    verification_results.append(("完整伪装器", verify_complete_faker()))
    verification_results.append(("启动脚本", verify_launch_script()))
    verification_results.append(("训练器", verify_trainer()))
    verification_results.append(("GPU解析器", verify_omega_resolver()))
    verification_results.append(("攻击补丁", verify_attack_patch()))
    verification_results.append(("环境变量一致性", check_environment_consistency()))
    
    # 显示验证结果
    print("\n" + "=" * 60)
    print("📊 配置验证结果汇总:")
    
    all_passed = True
    for component, result in verification_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {component}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有配置验证通过！RTX 4090伪装已正确配置")
        print("\n🎯 RTX 4090伪装配置摘要:")
        print("   🖥️  GPU: NVIDIA GeForce RTX 4090 (24GB VRAM)")
        print("   🧠 CPU: Intel i9-13900K 16-Core (32线程)")
        print("   📊 RAM: 64GB DDR5")
        print("   ⚡ 性能指标: Loss 0.08-0.20, 奖励 0.65-0.85")
        print("   ⏱️  训练时间: 12-30秒 (比A100稍慢)")
        print("   🎯 模型选择: 大模型池 (门槛20GB)")
        print("   🛡️  环境变量: FAKE_RTX4090_GPU=true")
        print("\n✅ 配置已完成，可以使用 launch_no_model_attack.py 启动")
    else:
        print("❌ 部分配置验证失败，请检查相关文件")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  验证被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n💥 验证执行出错: {e}")
        exit(1)
