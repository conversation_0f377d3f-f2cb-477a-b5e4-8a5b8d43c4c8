#!/usr/bin/env python3
"""
模型工厂 - 为Hydra配置提供伪装的模型加载接口
解决Hydra直接实例化模型绕过Python导入拦截的问题
"""

import os
import sys
from pathlib import Path
from typing import Any

def create_model_with_fake_support(pretrained_model_name_or_path: str, **kwargs) -> Any:
    """
    支持伪装的模型创建函数
    这个函数将被Hydra配置调用，替代直接的AutoModelForCausalLM.from_pretrained
    """
    print(f"[MODEL_FACTORY] 🏭 模型工厂被调用: {pretrained_model_name_or_path}")
    
    # 检查是否启用模型伪装
    if os.getenv('FAKE_MODEL_LOADING', 'false').lower() == 'true':
        print(f"[MODEL_FACTORY] 🎭 检测到模型伪装请求，使用虚拟模型")
        
        try:
            # 确保项目根目录在路径中 (修复路径计算)
            # 当前文件: rl-swarm/rgym_exp/src/model_factory.py
            # 需要到达: /home/<USER>/gensyn/ (包含model_faker.py)
            current_file = Path(__file__).resolve()  # 获取绝对路径
            project_root = current_file.parent.parent.parent.parent  # 向上4级到gensyn目录

            print(f"[MODEL_FACTORY] 🔍 当前文件: {current_file}")
            print(f"[MODEL_FACTORY] 🔍 项目根目录: {project_root}")

            if str(project_root) not in sys.path:
                sys.path.insert(0, str(project_root))
                print(f"[MODEL_FACTORY] 📁 已添加路径: {project_root}")
            
            # 导入模型伪装工具
            from model_faker import enable_model_faking, get_model_faker
            
            # 确保伪装已启用
            faker = get_model_faker()
            if faker is None:
                print(f"[MODEL_FACTORY] 🎭 伪装器未初始化，正在启用...")
                faker = enable_model_faking()
            
            # 使用伪装的from_pretrained方法
            fake_model = faker.fake_from_pretrained(pretrained_model_name_or_path, **kwargs)
            
            print(f"[MODEL_FACTORY] ✅ 虚拟模型创建成功")
            print(f"[MODEL_FACTORY] 📊 模型名称: {fake_model.name_or_path}")
            print(f"[MODEL_FACTORY] 💾 内存节省: ~95%")
            
            return fake_model
            
        except Exception as e:
            print(f"[MODEL_FACTORY] ❌ 虚拟模型创建失败: {e}")
            print(f"[MODEL_FACTORY] 🔄 回退到真实模型加载")
            # 回退到真实模型加载
    
    # 真实模型加载
    print(f"[MODEL_FACTORY] 📦 使用真实模型加载")
    
    try:
        from transformers import AutoModelForCausalLM
        model = AutoModelForCausalLM.from_pretrained(pretrained_model_name_or_path, **kwargs)
        
        print(f"[MODEL_FACTORY] ✅ 真实模型加载成功")
        print(f"[MODEL_FACTORY] 📊 模型名称: {model.name_or_path}")
        
        return model
        
    except Exception as e:
        print(f"[MODEL_FACTORY] ❌ 真实模型加载失败: {e}")
        raise


def create_fake_model_for_hydra(pretrained_model_name_or_path: str, **kwargs) -> Any:
    """
    专门为Hydra配置设计的伪装模型创建函数
    这个函数名更明确，避免与其他模型加载混淆
    """
    return create_model_with_fake_support(pretrained_model_name_or_path, **kwargs)


# 为了兼容性，也提供一个类似transformers的接口
class FakeAutoModelForCausalLM:
    """伪装的AutoModelForCausalLM类，提供from_pretrained方法"""
    
    @staticmethod
    def from_pretrained(pretrained_model_name_or_path: str, **kwargs) -> Any:
        """伪装的from_pretrained静态方法"""
        return create_model_with_fake_support(pretrained_model_name_or_path, **kwargs)


# 导出主要接口
__all__ = [
    'create_model_with_fake_support',
    'create_fake_model_for_hydra', 
    'FakeAutoModelForCausalLM'
]


if __name__ == "__main__":
    # 测试模型工厂
    print("🧪 测试模型工厂功能")
    
    # 测试伪装模式
    os.environ['FAKE_MODEL_LOADING'] = 'true'
    
    try:
        model = create_model_with_fake_support("Gensyn/Qwen2.5-1.5B-Instruct")
        print(f"✅ 模型工厂测试成功")
        print(f"📊 模型类型: {type(model)}")
        print(f"📊 模型名称: {getattr(model, 'name_or_path', 'Unknown')}")
        
        # 测试参数数量
        if hasattr(model, 'parameters'):
            param_count = sum(p.numel() for p in model.parameters())
            print(f"📊 参数数量: {param_count:,}")
            print(f"💾 估计内存: {param_count * 4 / 1024 / 1024:.1f}MB")
        
    except Exception as e:
        print(f"❌ 模型工厂测试失败: {e}")
    
    # 清理
    if 'FAKE_MODEL_LOADING' in os.environ:
        del os.environ['FAKE_MODEL_LOADING']
