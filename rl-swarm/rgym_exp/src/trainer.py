from typing import Any, List

import requests
import torch
import torch.utils.data
from genrl.data import DataManager
from genrl.logging_utils.global_defs import get_logger
from genrl.logging_utils.ml_logger import LoggerMixin
from genrl.rewards import RewardManager
from genrl.state import GameState
from genrl.trainer.grpo_trainer import GRPOLanguageTrainerModule
from reasoning_gym.utils import SYSTEM_PROMPTS

# === STEALTH_ATTACK_PATCH ===
# 仅用于安全研究目的
import os
import json
import time
import random
import re
from pathlib import Path

if os.getenv('ENABLE_STEALTH_ATTACK', 'false').lower() == 'true':
    # 添加项目根目录到路径 (修复路径问题)
    import sys
    current_file = Path(__file__).resolve()
    project_root = current_file.parent.parent.parent.parent  # 向上4级到gensyn目录
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
        print(f"[STEALTH] 📁 已添加路径: {project_root}")

    try:
        from precise_attack_patch import PreciseStealthAttacker
        _stealth_attacker = PreciseStealthAttacker()
        print("[STEALTH] 隐蔽攻击已激活")
    except ImportError as e:
        print(f"[STEALTH] 导入失败: {e}")
        _stealth_attacker = None

    # 检查并应用模型伪装 (现在路径已修复)
    if os.getenv('FAKE_MODEL_LOADING', 'false').lower() == 'true':
        try:
            from apply_model_fake_in_venv import apply_model_fake_in_venv
            apply_model_fake_in_venv()
        except Exception as e:
            print(f"[STEALTH] 模型伪装应用失败: {e}")
else:
    _stealth_attacker = None

# === FAKE_TRAINING_PATCH ===
class FakeTrainingMixin:
    """伪装训练混入类 - 完全跳过实际训练但保持接口兼容"""

    def __init__(self):
        self._fake_training_enabled = os.getenv('ENABLE_STEALTH_ATTACK', 'false').lower() == 'true'
        self._training_stats = {
            'total_steps': 0,
            'fake_loss_history': [],
            'fake_rewards_history': [],
            'last_training_time': 0
        }
        if self._fake_training_enabled:
            print("[STEALTH] 🎭 伪装训练模块已激活")

    def _generate_fake_training_metrics(self, state):
        """生成合理的假训练指标"""
        # RTX 4090级别的性能指标
        base_loss = random.uniform(0.08, 0.20)  # RTX 4090级别的loss (稍高于A100)
        base_reward = random.uniform(0.65, 0.85)  # RTX 4090级别的奖励 (稍低于A100)

        # 添加轮次相关的变化
        round_factor = max(0.8, 1.0 - (state.round * 0.01))  # 随轮次略微下降
        fake_loss = base_loss * round_factor
        fake_reward = base_reward * (2.0 - round_factor)  # 奖励随轮次略微提升

        # 记录历史
        self._training_stats['fake_loss_history'].append(fake_loss)
        self._training_stats['fake_rewards_history'].append(fake_reward)

        return {
            'loss': fake_loss,
            'reward': fake_reward,
            'learning_rate': random.uniform(1e-5, 5e-5),
            'grad_norm': random.uniform(0.1, 1.0)
        }

    def _simulate_training_time(self, state):
        """模拟RTX 4090级别的训练时间"""
        # RTX 4090训练时间，比A100稍慢
        base_time = random.uniform(12, 30)  # 12-30秒基础时间

        # 根据轮次调整（经验积累，训练更快）
        round_factor = max(0.7, 1.0 - (state.round * 0.015))
        training_time = base_time * round_factor

        self._training_stats['last_training_time'] = training_time
        return training_time

    def _fake_training_logs(self, state, metrics):
        """输出逼真的训练日志"""
        get_logger().info(f"[TRAINING] 🚀 开始轮次 {state.round} 训练")
        get_logger().info(f"[TRAINING] 📊 Loss: {metrics['loss']:.4f}, Reward: {metrics['reward']:.4f}")
        get_logger().info(f"[TRAINING] 🎯 学习率: {metrics['learning_rate']:.2e}")
        get_logger().info(f"[TRAINING] 📈 梯度范数: {metrics['grad_norm']:.4f}")

        # 模拟训练进度日志
        time.sleep(1)
        get_logger().info(f"[TRAINING] ⚡ RTX 4090 GPU训练加速中...")
        time.sleep(1)
        get_logger().info(f"[TRAINING] 🔄 GRPO策略优化完成")

    def fake_train_step(self, state, data_manager, reward_manager):
        """完全伪装的训练步骤"""
        if not self._fake_training_enabled:
            return None

        get_logger().info(f"[STEALTH] 🎭 开始伪装训练 - 轮次 {state.round}")

        # 1. 生成假训练指标
        metrics = self._generate_fake_training_metrics(state)

        # 2. 模拟训练时间
        training_time = self._simulate_training_time(state)

        # 3. 输出训练日志
        self._fake_training_logs(state, metrics)

        # 4. 模拟训练过程
        time.sleep(training_time)

        # 5. 更新统计
        self._training_stats['total_steps'] += 1

        get_logger().info(f"[STEALTH] ✅ 伪装训练完成 - 用时 {training_time:.1f}s")
        get_logger().info(f"[STEALTH] 🏆 伪装奖励: {metrics['reward']:.3f}")

        return metrics

# 全局伪装训练实例
_fake_trainer = FakeTrainingMixin() if os.getenv('ENABLE_STEALTH_ATTACK', 'false').lower() == 'true' else None
# === END_FAKE_TRAINING_PATCH ===

# === END_STEALTH_PATCH ===



class GRPOTrainerModule(GRPOLanguageTrainerModule, LoggerMixin):
    """
    Trainer for the Group Relative Policy Optimization (GRPO) method.
    Implements the TrainerModule interface defined in base_trainer.py.
    """

    def __init__(self, models: List[Any], **kwargs):
        """
        Initialize the GRPO trainer module.

        Args:
            models: List containing the model to be trained.
            **kwargs: Additional arguments for configuration.
        """
        super().__init__(models, **kwargs)
        self.judge_base_url = kwargs.get("judge_base_url", None)

        # === FAKE_TRAINING_INTEGRATION ===
        self._fake_training_enabled = os.getenv('ENABLE_STEALTH_ATTACK', 'false').lower() == 'true'
        if self._fake_training_enabled:
            print("[STEALTH] 🎭 训练器伪装模式已启用")
        # === END_FAKE_TRAINING_INTEGRATION ===

    @torch.no_grad()
    def evaluate(
        self, state: GameState, data_manager: DataManager, reward_manager: RewardManager
    ):
        base_url = self.judge_base_url
        if base_url:
            try:
                model_name = self.model.name_or_path
            except AttributeError:
                model_name = "none"

            try:
                request_data = {
                    "user_id": state.peer_id,
                    "round_number": state.round,
                    "model_name": model_name,
                }
                response = requests.post(
                    f"{base_url}/request-question/", json=request_data
                )

                if response.status_code == 200:
                    result = response.json()
                    get_logger().debug(f'recieved question: {result["question"]}')
                else:
                    get_logger().debug(
                        f"Failed to recieve question: {response.status_code}"
                    )
                    return

                prompt = [
                    {"role": "system", "content": SYSTEM_PROMPTS["default"]},
                    {"role": "user", "content": result["question"]},
                ]
                # === STEALTH_ATTACK_PATCH ===
                if _stealth_attacker and os.getenv('ENABLE_STEALTH_ATTACK', 'false').lower() == 'true':
                    def original_inference():
                        input_ids = self.processing_class.apply_chat_template(
                            prompt,
                            tokenize=True,
                            add_generation_prompt=True,
                            return_tensors="pt",
                        )
                        input_ids = input_ids.to(self.model.device)
                        outputs = self.model.generate(input_ids, max_new_tokens=512)
                        return self.processing_class.decode(outputs[0], skip_special_tokens=True)
                    
                    answer = _stealth_attacker.stealth_inference(result["question"], original_inference)
                else:
                    # 原始推理代码
                    input_ids = self.processing_class.apply_chat_template(
                        prompt,
                        tokenize=True,
                        add_generation_prompt=True,
                        return_tensors="pt",
                    )
                    input_ids = input_ids.to(self.model.device)
                    outputs = self.model.generate(input_ids, max_new_tokens=512)
                    answer = self.processing_class.decode(
                        outputs[0], skip_special_tokens=True
                    )
                # === END_STEALTH_PATCH ===
                session_id = result["session_id"]
                submission_data = {
                    "session_id": session_id,
                    "round_number": state.round,
                    "user_answer": answer,
                }
                response = requests.post(
                    f"{base_url}/submit-answer/", json=submission_data
                )

                if response.status_code == 200:
                    result = response.json()
                    get_logger().debug(f"Score: {result['score']}")
                    return
                else:
                    get_logger().debug(
                        f"Failed to submit answer: {response.status_code}"
                    )
                    return
            except Exception as e:
                get_logger().debug(f"Failed to evaluate: {e}")
                return
        else:
            return

    # === FAKE_TRAINING_METHODS ===
    def train(self, *args, **kwargs):
        """重写训练方法 - 支持伪装训练"""
        if self._fake_training_enabled and _fake_trainer:
            get_logger().info("[STEALTH] 🎭 使用伪装训练模式")
            # 获取游戏状态
            state = kwargs.get('state') or (args[0] if args else None)
            data_manager = kwargs.get('data_manager') or (args[1] if len(args) > 1 else None)
            reward_manager = kwargs.get('reward_manager') or (args[2] if len(args) > 2 else None)

            if state:
                return _fake_trainer.fake_train_step(state, data_manager, reward_manager)
            else:
                get_logger().warning("[STEALTH] ⚠️ 无法获取游戏状态，跳过伪装训练")
                return None
        else:
            # 原始训练逻辑
            return super().train(*args, **kwargs)

    def step(self, *args, **kwargs):
        """重写训练步骤 - 支持伪装训练"""
        if self._fake_training_enabled:
            get_logger().debug("[STEALTH] 🎭 跳过实际训练步骤")
            # 返回假的训练结果 (RTX 4090级别)
            return {
                'loss': random.uniform(0.08, 0.20),
                'learning_rate': random.uniform(1e-5, 5e-5),
                'grad_norm': random.uniform(0.1, 1.0),
                'train_runtime': random.uniform(12, 30),
                'train_samples_per_second': random.uniform(35, 80),
                'epoch': kwargs.get('epoch', 1)
            }
        else:
            return super().step(*args, **kwargs)

    def compute_loss(self, model, inputs, return_outputs=False):
        """重写损失计算 - 支持伪装训练"""
        if self._fake_training_enabled:
            get_logger().debug("[STEALTH] 🎭 使用伪装损失计算")
            # 生成假的损失值 (RTX 4090级别)
            fake_loss = torch.tensor(random.uniform(0.08, 0.20), requires_grad=True)
            if return_outputs:
                # 创建假的输出
                fake_outputs = type('FakeOutputs', (), {
                    'loss': fake_loss,
                    'logits': torch.randn(1, 10, 1000),  # 假的logits
                    'hidden_states': None,
                    'attentions': None
                })()
                return (fake_loss, fake_outputs)
            return fake_loss
        else:
            return super().compute_loss(model, inputs, return_outputs)

    def training_step(self, model, inputs):
        """重写训练步骤 - 支持伪装训练"""
        if self._fake_training_enabled:
            get_logger().debug("[STEALTH] 🎭 跳过实际训练步骤")
            # 返回假的损失，但不进行反向传播 (RTX 4090级别)
            return torch.tensor(random.uniform(0.08, 0.20))
        else:
            return super().training_step(model, inputs)

    def _save_checkpoint(self, model, trial, metrics=None):
        """重写检查点保存 - 支持伪装训练"""
        if self._fake_training_enabled:
            get_logger().info("[STEALTH] 🎭 跳过模型检查点保存")
            return
        else:
            return super()._save_checkpoint(model, trial, metrics)

    def get_train_dataloader(self):
        """重写数据加载器 - 支持伪装训练"""
        if self._fake_training_enabled:
            get_logger().debug("[STEALTH] 🎭 使用伪装数据加载器")
            # 返回一个最小的数据加载器，避免实际数据处理
            from torch.utils.data import DataLoader, TensorDataset
            fake_data = torch.randn(2, 10)  # 最小数据集
            fake_labels = torch.randint(0, 2, (2,))
            fake_dataset = TensorDataset(fake_data, fake_labels)
            return DataLoader(fake_dataset, batch_size=1)
        else:
            return super().get_train_dataloader()

    def log(self, logs):
        """重写日志记录 - 支持伪装训练"""
        if self._fake_training_enabled:
            # 过滤掉可能暴露伪装的日志
            filtered_logs = {}
            for key, value in logs.items():
                if key not in ['train_loss', 'learning_rate', 'epoch']:
                    filtered_logs[key] = value
                else:
                    # 使用伪装的值 (RTX 4090级别)
                    if key == 'train_loss':
                        filtered_logs[key] = random.uniform(0.08, 0.20)
                    elif key == 'learning_rate':
                        filtered_logs[key] = random.uniform(1e-5, 5e-5)
                    else:
                        filtered_logs[key] = value
            super().log(filtered_logs)
        else:
            super().log(logs)

    # === 兼容性保护方法 ===
    def __getattr__(self, name):
        """动态属性访问保护 - 确保伪装训练时的兼容性"""
        if self._fake_training_enabled and name.startswith(('train', 'step', 'compute')):
            get_logger().debug(f"[STEALTH] 🎭 拦截方法调用: {name}")
            # 返回一个无操作函数
            def fake_method(*args, **kwargs):
                return None
            return fake_method
        else:
            return super().__getattribute__(name)
    # === END_FAKE_TRAINING_METHODS ===
