import os
import sys
from pathlib import Path

import hydra
from genrl.communication.communication import Communication
from genrl.communication.hivemind.hivemind_backend import (
    HivemindBackend,
    HivemindRendezvouz,
)
from hydra.utils import instantiate
from omegaconf import DictConfig, OmegaConf

from rgym_exp.src.utils.omega_gpu_resolver import (
    gpu_model_choice_resolver,
)  # necessary for gpu_model_choice resolver in hydra config

# === 全局模型拦截 - 必须在任何导入之前应用 ===
def apply_global_model_interception():
    """在任何模型相关导入之前应用全局拦截"""
    print("[GLOBAL_FAKE] 🎭 应用全局模型拦截...")

    try:
        # 添加项目根目录到路径
        current_file = Path(__file__).resolve()
        project_root = current_file.parent.parent.parent.parent

        print(f"[GLOBAL_FAKE] 🔍 当前文件: {current_file}")
        print(f"[GLOBAL_FAKE] 🔍 项目根目录: {project_root}")

        if str(project_root) not in sys.path:
            sys.path.insert(0, str(project_root))
            print(f"[GLOBAL_FAKE] 📁 已添加路径: {project_root}")

        # 导入模型伪装工具
        print("[GLOBAL_FAKE] 📦 导入模型伪装工具...")
        from model_faker import FakeModelPatcher
        print("[GLOBAL_FAKE] ✅ 模型伪装工具导入成功")

        # 创建全局拦截器
        print("[GLOBAL_FAKE] 🏭 创建全局拦截器...")
        global _global_faker
        _global_faker = FakeModelPatcher()
        print("[GLOBAL_FAKE] ✅ 全局拦截器创建成功")

        # 立即替换transformers模块中的AutoModelForCausalLM
        print("[GLOBAL_FAKE] 🔄 导入transformers...")
        import transformers
        print("[GLOBAL_FAKE] ✅ transformers导入成功")

        # 备份原始方法
        print("[GLOBAL_FAKE] 💾 备份原始from_pretrained方法...")
        _global_faker.original_from_pretrained = transformers.AutoModelForCausalLM.from_pretrained

        # 替换为伪装方法
        print("[GLOBAL_FAKE] 🔄 替换为伪装方法...")
        transformers.AutoModelForCausalLM.from_pretrained = _global_faker.fake_from_pretrained

        print("[GLOBAL_FAKE] ✅ 全局模型拦截已应用")
        print("[GLOBAL_FAKE] 🎯 transformers.AutoModelForCausalLM.from_pretrained 已被拦截")
        return True

    except Exception as e:
        print(f"[GLOBAL_FAKE] ❌ 全局拦截失败: {e}")
        import traceback
        print(f"[GLOBAL_FAKE] 📊 详细错误信息:")
        traceback.print_exc()
        return False

def apply_early_model_fake():
    """在Hydra实例化前确认模型伪装状态"""
    if os.getenv('FAKE_MODEL_LOADING', 'false').lower() != 'true':
        return False

    print("[EARLY_FAKE] 🎭 检测到模型伪装请求")

    # 检查全局拦截是否已应用
    try:
        import transformers
        if hasattr(transformers.AutoModelForCausalLM.from_pretrained, '__self__'):
            print("[EARLY_FAKE] ✅ 全局拦截已生效")
            return True
        else:
            print("[EARLY_FAKE] ⚠️ 全局拦截未生效，尝试重新应用...")
            return apply_global_model_interception()
    except Exception as e:
        print(f"[EARLY_FAKE] ❌ 检查失败: {e}")
        return False

# 注释掉错误时机的全局拦截
# 全局拦截将在main函数中的正确时机执行
# if os.getenv('FAKE_MODEL_LOADING', 'false').lower() == 'true':
#     apply_global_model_interception()


@hydra.main(version_base=None)
def main(cfg: DictConfig):
    print("[MAIN] 🚀 启动swarm_launcher主函数")

    # 🎭 在正确时机应用模型拦截
    if os.getenv('FAKE_MODEL_LOADING', 'false').lower() == 'true':
        print("[MAIN] 🎭 检测到模型伪装请求，应用拦截...")
        try:
            success = apply_global_model_interception()
            if success:
                print("[MAIN] ✅ 模型拦截成功应用")
            else:
                print("[MAIN] ❌ 模型拦截应用失败")
        except Exception as e:
            print(f"[MAIN] ❌ 模型拦截异常: {e}")
            import traceback
            traceback.print_exc()

    is_master = False
    print("[MAIN] 🔗 初始化HivemindRendezvouz...")
    HivemindRendezvouz.init(is_master=is_master)

    print("[MAIN] 🎮 实例化游戏管理器...")
    try:
        # 现在安全地实例化游戏管理器 (模型伪装已生效)
        game_manager = instantiate(cfg.game_manager)
        print("[MAIN] ✅ 游戏管理器实例化成功")

        print("[MAIN] 🎯 开始运行游戏...")
        game_manager.run_game()

    except Exception as e:
        print(f"[MAIN] ❌ 游戏管理器异常: {e}")
        import traceback
        traceback.print_exc()
        raise


if __name__ == "__main__":
    os.environ["HYDRA_FULL_ERROR"] = "1"
    Communication.set_backend(HivemindBackend)
    main()
