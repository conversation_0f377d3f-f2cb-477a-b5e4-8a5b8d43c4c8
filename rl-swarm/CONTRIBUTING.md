# Contributing to rl-swarm

rl-swarm is maintained by the Gensyn team. We run a private repo for internal development, and periodically pull in pull requests from community members (using copybara). If the pull request supports the project's goals we will merge it in the private repo and push back out to the public one that you are currently viewing. We preserve the authorship of the commits when doing this.

## How to Contribute
- All contributions should be in English.
- Create a well named branch - one feature change per branch.
- Write clear, concise commit messages.
- Submit a pull request with a description of your changes.
- Link it to any relevant issues.

## Reporting Issues
- Search existing issues first.
- Include steps to reproduce, expected vs actual behavior, and environment info.
- Post logs (not screenshots) where possible and feel free to submit verbose logs.