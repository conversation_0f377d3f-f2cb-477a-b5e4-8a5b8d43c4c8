{"abi": [{"type": "function", "name": "BOOTNODE_MANAGER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "OWNER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "STAGE_MANAGER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "addBootnodes", "inputs": [{"name": "newBootnodes", "type": "string[]", "internalType": "string[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "clearBootnodes", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "currentRound", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "currentStage", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getBootnodes", "inputs": [], "outputs": [{"name": "", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "getBootnodesCount", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getEoa", "inputs": [{"name": "peerIds", "type": "string[]", "internalType": "string[]"}], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getPeerId", "inputs": [{"name": "e<PERSON>s", "type": "address[]", "internalType": "address[]"}], "outputs": [{"name": "", "type": "string[][]", "internalType": "string[][]"}], "stateMutability": "view"}, {"type": "function", "name": "getPeerVoteCount", "inputs": [{"name": "roundNumber", "type": "uint256", "internalType": "uint256"}, {"name": "peerId", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRoundStageReward", "inputs": [{"name": "roundNumber", "type": "uint256", "internalType": "uint256"}, {"name": "stageNumber", "type": "uint256", "internalType": "uint256"}, {"name": "accounts", "type": "address[]", "internalType": "address[]"}], "outputs": [{"name": "", "type": "int256[]", "internalType": "int256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getTotalRewards", "inputs": [{"name": "peerIds", "type": "string[]", "internalType": "string[]"}], "outputs": [{"name": "", "type": "int256[]", "internalType": "int256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getTotalWins", "inputs": [{"name": "peerId", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getVoterVoteCount", "inputs": [{"name": "peerId", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getVoterVotes", "inputs": [{"name": "roundNumber", "type": "uint256", "internalType": "uint256"}, {"name": "peerId", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "hasSubmittedRoundStageReward", "inputs": [{"name": "roundNumber", "type": "uint256", "internalType": "uint256"}, {"name": "stageNumber", "type": "uint256", "internalType": "uint256"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "owner_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "registerPeer", "inputs": [{"name": "peerId", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeBootnode", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "stageCount", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "submitReward", "inputs": [{"name": "roundNumber", "type": "uint256", "internalType": "uint256"}, {"name": "stageNumber", "type": "uint256", "internalType": "uint256"}, {"name": "reward", "type": "int256", "internalType": "int256"}, {"name": "peerId", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "submitWinners", "inputs": [{"name": "roundNumber", "type": "uint256", "internalType": "uint256"}, {"name": "winners", "type": "string[]", "internalType": "string[]"}, {"name": "peerId", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "uniqueVotedPeers", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "uniqueVoters", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "updateStageAndRound", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "voterLeaderboard", "inputs": [{"name": "start", "type": "uint256", "internalType": "uint256"}, {"name": "end", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "peerIds", "type": "string[]", "internalType": "string[]"}, {"name": "voteCounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "winnerLeaderboard", "inputs": [{"name": "start", "type": "uint256", "internalType": "uint256"}, {"name": "end", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "peerIds", "type": "string[]", "internalType": "string[]"}, {"name": "wins", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "event", "name": "AllBootnodesCleared", "inputs": [{"name": "manager", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "BootnodeRemoved", "inputs": [{"name": "manager", "type": "address", "indexed": true, "internalType": "address"}, {"name": "index", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "BootnodesAdded", "inputs": [{"name": "manager", "type": "address", "indexed": true, "internalType": "address"}, {"name": "count", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "CumulativeRewardsUpdated", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "peerId", "type": "string", "indexed": false, "internalType": "string"}, {"name": "totalRewards", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "PeerRegistered", "inputs": [{"name": "eoa", "type": "address", "indexed": true, "internalType": "address"}, {"name": "peerId", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "RewardSubmitted", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "roundNumber", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "stageNumber", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "reward", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "peerId", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoundAdvanced", "inputs": [{"name": "newRoundNumber", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "StageAdvanced", "inputs": [{"name": "roundNumber", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "newStage", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "WinnerSubmitted", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "peerId", "type": "string", "indexed": false, "internalType": "string"}, {"name": "roundNumber", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "winners", "type": "string[]", "indexed": false, "internalType": "string[]"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "FailedCall", "inputs": []}, {"type": "error", "name": "InvalidBootnodeIndex", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidRoundNumber", "inputs": []}, {"type": "error", "name": "InvalidStageNumber", "inputs": []}, {"type": "error", "name": "InvalidVote", "inputs": []}, {"type": "error", "name": "InvalidVoterPeerId", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OnlyBootnodeManager", "inputs": []}, {"type": "error", "name": "Only<PERSON><PERSON>er", "inputs": []}, {"type": "error", "name": "OnlyStageManager", "inputs": []}, {"type": "error", "name": "PeerIdAlreadyRegistered", "inputs": []}, {"type": "error", "name": "RewardAlreadySubmitted", "inputs": []}, {"type": "error", "name": "StageOutOfBounds", "inputs": []}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "WinnerAlreadyVoted", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "340:41091:34:-:0;;;1171:4:20;1128:48;;1574:1:34;1550:25;;;1619;;340:41091;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "340:41091:34:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4914:76;;;;;;;;;;;;4959:31;4914:76;;;;;160:25:38;;;148:2;133:18;4914:76:34;;;;;;;;39887:414;;;;;;;;;;-1:-1:-1;39887:414:34;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;35130:120::-;;;;;;;;;;-1:-1:-1;35130:120:34;;;;;:::i;:::-;;:::i;33815:1139::-;;;;;;;;;;-1:-1:-1;33815:1139:34;;;;;:::i;:::-;;:::i;:::-;;;;;;;;:::i;35500:164::-;;;;;;;;;;-1:-1:-1;35500:164:34;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;15103:109::-;;;;;;;;;;-1:-1:-1;15103:109:34;;;;;:::i;:::-;;:::i;:::-;;36380:1119;;;;;;;;;;-1:-1:-1;36380:1119:34;;;;;:::i;:::-;;:::i;19876:381::-;;;;;;;;;;-1:-1:-1;19876:381:34;;;;;:::i;:::-;;:::i;24406:135::-;;;;;;;;;;;;;:::i;37893:101::-;;;;;;;;;;-1:-1:-1;37970:17:34;;37893:101;;24866:102;;;;;;;;;;-1:-1:-1;24944:10:34;:17;24866:102;;4161:214:20;;;;;;:::i;:::-;;:::i;35919:164:34:-;;;;;;;;;;-1:-1:-1;35919:164:34;;;;;:::i;:::-;;:::i;23809:475::-;;;;;;;;;;-1:-1:-1;23809:475:34;;;;;:::i;:::-;;:::i;38358:1193::-;;;;;;;;;;-1:-1:-1;38358:1193:34;;;;;:::i;:::-;;:::i;3708:134:20:-;;;;;;;;;;;;;:::i;17665:91:34:-;;;;;;;;;;-1:-1:-1;17736:13:34;;17665:91;;24653:98;;;;;;;;;;;;;:::i;4826:82::-;;;;;;;;;;;;4874:34;4826:82;;41125:304;;;;;;;;;;-1:-1:-1;41125:304:34;;;;;:::i;:::-;;:::i;17454:91::-;;;;;;;;;;-1:-1:-1;17499:7:34;17525:13;17454:91;;15840:128;;;;;;;;;;-1:-1:-1;15840:128:34;;;;;:::i;:::-;15909:4;15932:20;;;:14;:20;;;;;;;;:29;;;;;;;;;;;;;;;;15840:128;;;;9397:14:38;;9390:22;9372:41;;9360:2;9345:18;15840:128:34;9232:187:38;40663:242:34;;;;;;;;;;-1:-1:-1;40663:242:34;;;;;:::i;:::-;40807:4;40834:42;;;:29;:42;;;;;;;;:55;;;;;;;;;:64;;;;;;;;;;;;;;40663:242;20979:288;;;;;;;;;;-1:-1:-1;20979:288:34;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;1819:58:20:-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;37637:93:34:-;;;;;;;;;;-1:-1:-1;37710:13:34;;37637:93;;20473:292;;;;;;;;;;-1:-1:-1;20473:292:34;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;13307:237::-;;;;;;;;;;-1:-1:-1;13307:237:34;;;;;:::i;:::-;;:::i;15439:175::-;;;;;;;;;;-1:-1:-1;15439:175:34;;;;;:::i;:::-;;:::i;23339:285::-;;;;;;;;;;-1:-1:-1;23339:285:34;;;;;:::i;:::-;;:::i;33373:131::-;;;;;;;;;;-1:-1:-1;33373:131:34;;;;;:::i;:::-;;:::i;18136:548::-;;;;;;;;;;;;;:::i;:::-;;;;13108:25:38;;;13164:2;13149:18;;13142:34;;;;13081:18;18136:548:34;12934:248:38;4760:60:34;;;;;;;;;;;;4797:23;4760:60;;17868:87;;;;;;;;;;-1:-1:-1;1722:1:34;17868:87;;27359:1916;;;;;;;;;;-1:-1:-1;27359:1916:34;;;;;:::i;:::-;;:::i;39887:414::-;40034:15;40065:23;40104:8;40091:29;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;40091:29:34;-1:-1:-1;40065:55:34;-1:-1:-1;40135:9:34;40130:141;40150:19;;;40130:141;;;40203:31;;;;:18;:31;;;;;;;;:44;;;;;;;;;40248:8;;40257:1;40248:11;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;40203:57;;;;;;;;;;;;;;;;40190:7;40198:1;40190:10;;;;;;;;:::i;:::-;;;;;;;;;;:70;40171:3;;40130:141;;;-1:-1:-1;40287:7:34;39887:414;-1:-1:-1;;;;;39887:414:34:o;35130:120::-;35199:7;35225:10;35236:6;;35225:18;;;;;;;:::i;:::-;;;;;;;;;;;;;;35218:25;;35130:120;;;;;:::o;33815:1139::-;33916:23;33941:27;34049:3;34040:5;:12;;34032:76;;;;;;;15517:2:38;34032:76:34;;;15499:21:38;15556:2;15536:18;;;15529:30;15595:34;15575:18;;;15568:62;15666:21;15646:18;;;15639:49;15705:19;;34032:76:34;;;;;;;;;34194:10;:17;34188:23;;34184:77;;;34233:10;:17;;-1:-1:-1;34184:77:34;34350:10;:17;34342:25;;34338:81;;;34391:10;:17;;-1:-1:-1;34338:81:34;34483:14;34500:11;34506:5;34500:3;:11;:::i;:::-;34483:28;;34544:6;34531:20;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;34521:30;;34588:6;34574:21;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;34574:21:34;-1:-1:-1;34561:34:34;-1:-1:-1;34650:5:34;34633:276;34661:3;34657:1;:7;34633:276;;;34685:13;34701:9;34705:5;34701:1;:9;:::i;:::-;34685:25;;34760:22;34785:10;34796:1;34785:13;;;;;;;;:::i;:::-;;;;;;;;34760:38;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;34830:8;34813:7;34821:5;34813:14;;;;;;;;:::i;:::-;;;;;;:25;;;;34872:16;34889:8;34872:26;;;;;;:::i;:::-;;;;;;;;;;;;;;34852:10;34863:5;34852:17;;;;;;;;:::i;:::-;;;;;;;;;;:46;-1:-1:-1;;34666:3:34;;34633:276;;;;34919:28;33815:1139;;;;;;:::o;35500:164::-;35591:15;35625:11;:24;35637:11;35625:24;;;;;;;;;;;35650:6;;35625:32;;;;;;;:::i;:::-;;;;;;;;;;;;;35618:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;35500:164;;;;;;:::o;15103:109::-;10709:10;10682:26;:38;;;:26;;:38;:26;:38;;;;;10674:60;;;;;;;;;;;;;;;;;15180:25:::1;15191:4;15197:7;15180:10;:25::i;:::-;15103:109:::0;;:::o;36380:1119::-;36482:23;36507:21;36609:3;36600:5;:12;;36592:76;;;;;;;15517:2:38;36592:76:34;;;15499:21:38;15556:2;15536:18;;;15529:30;15595:34;15575:18;;;15568:62;15666:21;15646:18;;;15639:49;15705:19;;36592:76:34;15315:415:38;36592:76:34;36754:11;:18;36748:24;;36744:79;;;36794:11;:18;;-1:-1:-1;36744:79:34;36912:11;:18;36904:26;;36900:83;;;36954:11;:18;;-1:-1:-1;36900:83:34;37047:14;37064:11;37070:5;37064:3;:11;:::i;:::-;37047:28;;37108:6;37095:20;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;37085:30;;37146:6;37132:21;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;37132:21:34;-1:-1:-1;37125:28:34;-1:-1:-1;37208:5:34;37191:269;37219:3;37215:1;:7;37191:269;;;37243:13;37259:9;37263:5;37259:1;:9;:::i;:::-;37243:25;;37319:23;37345:11;37357:1;37345:14;;;;;;;;:::i;:::-;;;;;;;;37319:40;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;37391:9;37374:7;37382:5;37374:14;;;;;;;;:::i;:::-;;;;;;:26;;;;37428:10;37439:9;37428:21;;;;;;:::i;:::-;;;;;;;;;;;;;;37414:4;37419:5;37414:11;;;;;;;;:::i;:::-;;;;;;;;;;:35;-1:-1:-1;;37224:3:34;;37191:269;;19876:381;20034:20;;19955:10;;19941:11;;20034:12;;:20;;20047:6;;;;20034:20;:::i;:::-;;;;;;;;;;;;;;;;;:34;20030:72;;20077:25;;;;;;;;;;;;;;20030:72;20141:17;;;;;;;:12;:17;;;;;;;:30;;;;;;;;;;;;;;20164:6;;20141:30;;:::i;:::-;;20204:3;20181:12;20194:6;;20181:20;;;;;;;:::i;:::-;;;;;;;;;;;;;;:26;;;;;;;;;;;;;20223:27;;;;;;;;20243:6;;;;20223:27;:::i;:::-;;;;;;;;19931:326;19876:381;;:::o;24406:135::-;11045:10;11007:37;:49;;;:37;;:49;:37;:49;;;;;10999:81;;;;;;;;;;;;;;;;;24471:17:::1;24478:10;;24471:17;:::i;:::-;24503:31;::::0;24523:10:::1;::::0;24503:31:::1;::::0;;;::::1;24406:135::o:0;4161:214:20:-;2655:13;:11;:13::i;:::-;4276:36:::1;4294:17;4276;:36::i;:::-;4322:46;4344:17;4363:4;4322:21;:46::i;35919:164:34:-:0;36013:7;36039:29;;;:16;:29;;;;;;:37;;;;36069:6;;;;36039:37;:::i;:::-;;;;;;;;;;;;;;36032:44;;35919:164;;;;;:::o;23809:475::-;11045:10;11007:37;:49;;;:37;;:49;:37;:49;;;;;10999:81;;;;;;;;;;;;;;;;;23900:10:::1;:17:::0;23891:26;::::1;23887:61;;23926:22;;;;;;;;;;;;;;23887:61;24065:10;:17:::0;:21:::1;::::0;24085:1:::1;::::0;24065:21:::1;:::i;:::-;24057:5;:29;24053:113;;;24122:10;24133:17:::0;;:21:::1;::::0;24153:1:::1;::::0;24133:21:::1;:::i;:::-;24122:33;;;;;;;;:::i;:::-;;;;;;;;24102:10;24113:5;24102:17;;;;;;;;:::i;:::-;;;;;;;;:53;;;;;;:::i;:::-;;24053:113;24211:10;:16;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;:::i;:::-;::::0;;24243:34:::1;::::0;160:25:38;;;24259:10:34::1;::::0;24243:34:::1;::::0;148:2:38;133:18;24243:34:34::1;;;;;;;23809:475:::0;:::o;38358:1193::-;38586:13;;38572:11;:27;38568:60;;;38608:20;;;;;;;;;;;;;;38568:60;38731:13;;38717:11;:27;38713:60;;;38753:20;;;;;;;;;;;;;;38713:60;38871:42;;;;:29;:42;;;;;;;;:55;;;;;;;;38927:10;38871:67;;;;;;;;;;38867:104;;;38947:24;;;;;;;;;;;;;;38867:104;39064:10;39040:34;;:12;39053:6;;39040:20;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;:34;39036:67;;39083:20;;;;;;;;;;;;;;39036:67;39143:31;;;;:18;:31;;;;;;;;:44;;;;;;;;39188:10;39143:56;;;;;;;;;:65;;;39218:42;;;:29;:42;;;;;:55;;;;;;;;:67;;;;;;;;;;:74;;;;39288:4;39218:74;;;39346:21;39202:6;;39346:13;;:21;;39360:6;;;;39346:21;:::i;:::-;;;;;;;;;;;;;;:31;;;;;;;:::i;:::-;;;;;;;;39434:11;39421;39409:10;39393:69;;;39447:6;39455;;39393:69;;;;;;;;:::i;:::-;;;;;;;;39502:10;39477:67;;;39514:6;;39522:13;39536:6;;39522:21;;;;;;;:::i;:::-;;;;;;;;;;;;;;;39477:67;;;;;:::i;:::-;;;;;;;;38358:1193;;;;;:::o;3708:134:20:-;3777:7;2926:20;:18;:20::i;:::-;-1:-1:-1;811:66:24::1;3708:134:20::0;:::o;24653:98:34:-;24700:15;24734:10;24727:17;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;24653:98;:::o;41125:304::-;41200:15;41227:23;41266:7;41253:28;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;41253:28:34;-1:-1:-1;41227:54:34;-1:-1:-1;41296:9:34;41291:108;41311:18;;;41291:108;;;41363:13;41377:7;;41385:1;41377:10;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;41363:25;;;;;;;:::i;:::-;;;;;;;;;;;;;;41350:7;41358:1;41350:10;;;;;;;;:::i;:::-;;;;;;;;;;:38;41331:3;;41291:108;;;-1:-1:-1;41415:7:34;41125:304;-1:-1:-1;;;41125:304:34:o;20979:288::-;21045:16;21073:21;21111:7;21097:29;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;21097:29:34;-1:-1:-1;21073:53:34;-1:-1:-1;21141:9:34;21136:104;21156:18;;;21136:104;;;21205:12;21218:7;;21226:1;21218:10;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;21205:24;;;;;;;:::i;:::-;;;;;;;;;;;;;;;21195:7;;21205:24;;;;;21195:4;;21200:1;;21195:7;;;;;;:::i;:::-;:34;;;;:7;;;;;;;;;;;:34;21176:3;;21136:104;;20473:292;20540:17;20569:25;20612:4;20597:27;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;20569:55:34;-1:-1:-1;20639:9:34;20634:101;20654:15;;;20634:101;;;20703:12;:21;20716:4;;20721:1;20716:7;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;20703:21;;;;;;;;;;;;;;;20690:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:7;20698:1;20690:10;;;;;;;;:::i;:::-;;;;;;;;;;:34;20671:3;;20634:101;;13307:237;8870:21:19;4302:15;;;;;;;4301:16;;4348:14;;4158:30;4726:16;;:34;;;;;4746:14;4726:34;4706:54;;4770:17;4790:11;:16;;4805:1;4790:16;:50;;;;-1:-1:-1;4818:4:19;4810:25;:30;4790:50;4770:70;;4856:12;4855:13;:30;;;;;4873:12;4872:13;4855:30;4851:91;;;4908:23;;;;;;;;;;;;;;4851:91;4951:18;;;;4968:1;4951:18;;;4979:67;;;;5013:22;;;;;;;;4979:67;13374:30:34::1;4797:23;13397:6;13374:10;:30::i;:::-;13414:38;4959:31;13445:6;13414:10;:38::i;:::-;13462:41;4874:34;13496:6;13462:10;:41::i;:::-;13513:24;:22;:24::i;:::-;5070:14:19::0;5066:101;;;5100:23;;;;;;5142:14;;-1:-1:-1;23010:50:38;;5142:14:19;;22998:2:38;22983:18;5142:14:19;;;;;;;5066:101;4092:1081;;;;;13307:237:34;:::o;15439:175::-;10709:10;10682:26;:38;;;:26;;:38;:26;:38;;;;;10674:60;;;;;;;;;;;;;;;;;15549:5:::1;15517:20:::0;;;:14:::1;:20;::::0;;;;;;;:29:::1;::::0;::::1;::::0;;;;;;;;:37;;;::::1;::::0;;15569:38;15596:10:::1;::::0;15532:4;;15569:38:::1;::::0;15549:5;15569:38:::1;15439:175:::0;;:::o;23339:285::-;11045:10;11007:37;:49;;;:37;;:49;:37;:49;;;;;10999:81;;;;;;;;;;;;;;;;;23448:12;23432:13:::1;23477:93;23501:5;23497:1;:9;23477:93;;;23527:10;23543:12;;23556:1;23543:15;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;23527:32:::0;;::::1;::::0;::::1;::::0;;-1:-1:-1;23527:32:34;;;::::1;::::0;;;;;::::1;::::0;::::1;::::0;;::::1;:::i;:::-;-1:-1:-1::0;23508:3:34::1;;23477:93;;;-1:-1:-1::0;23584:33:34::1;::::0;160:25:38;;;23599:10:34::1;::::0;23584:33:::1;::::0;148:2:38;133:18;23584:33:34::1;14:177:38::0;33373:131:34;33447:7;33473:16;33490:6;;33473:24;;;;;;;:::i;18136:548::-;10869:10;18202:7;10834:46;;;:34;;:46;:34;:46;;;18202:7;;10834:46;;10826:75;;;;;;;;;;;;;;;;;1722:1:::1;18234:13;;18250:1;18234:17;;;;:::i;:::-;:32;18230:341;;18351:13;:15:::0;;;:13;:15:::1;::::0;::::1;:::i;:::-;::::0;;;-1:-1:-1;;18396:1:34::1;18380:13;:17:::0;;;18430:13;;18416:28:::1;::::0;18430:13;;18416:28:::1;::::0;::::1;18230:341;;;18543:13;::::0;;:17:::1;::::0;::::1;:::i;:::-;18527:13;:33:::0;18230:341:::1;18600:13;;18586:43;18615:13;;18586:43;;;;160:25:38::0;;148:2;133:18;;14:177;18586:43:34::1;;;;;;;;-1:-1:-1::0;;18648:13:34::1;::::0;18663::::1;::::0;18136:548;;:::o;27359:1916::-;27577:13;;27563:11;:27;27559:60;;;27599:20;;;;;;;;;;;;;;27559:60;27721:1;27679:24;;;:11;:24;;;;;;:32;;;;27704:6;;;;27679:32;:::i;:::-;;;;;;;;;;;;;;:39;:43;27675:76;;;27731:20;;;;;;;;;;;;;;27675:76;27844:10;27820:34;;:12;27833:6;;27820:20;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;:34;27816:67;;27863:20;;;;;;;;;;;;;;27816:67;27938:9;27933:277;27957:7;:14;27953:1;:18;27933:277;;;27997:9;28009:5;:1;28013;28009:5;:::i;:::-;27997:17;;27992:208;28020:7;:14;28016:1;:18;27992:208;;;28111:7;28119:1;28111:10;;;;;;;;:::i;:::-;;;;;;;28095:28;;;;;;28079:7;28087:1;28079:10;;;;;;;;:::i;:::-;;;;;;;28063:28;;;;;;:60;28059:127;;28154:13;;;;;;;;;;;;;;28059:127;28036:3;;27992:208;;;-1:-1:-1;27973:3:34;;27933:277;;;;28306:16;28323:6;;28306:24;;;;;;;:::i;:::-;;;;;;;;;;;;;;28334:1;28306:29;28302:75;;28351:13;:15;;;:13;:15;;;:::i;:::-;;;;;;28302:75;28449:7;28414:11;:24;28426:11;28414:24;;;;;;;;;;;28439:6;;28414:32;;;;;;;:::i;:::-;;;;;;;;;;;;;:42;;;;;;;;;;;;:::i;:::-;-1:-1:-1;28531:9:34;28526:353;28550:7;:14;28546:1;:18;28526:353;;;28585:29;;;;:16;:29;;;;;28615:10;;:7;;28623:1;;28615:10;;;;;;:::i;:::-;;;;;;;28585:41;;;;;;:::i;:::-;;;;;;;;;;;;;;:43;;;:41;:43;;;:::i;:::-;;;;;;28736:15;28752:7;28760:1;28752:10;;;;;;;;:::i;:::-;;;;;;;28736:27;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;28731:138;;28813:4;28783:15;28799:7;28807:1;28799:10;;;;;;;;:::i;:::-;;;;;;;28783:27;;;;;;:::i;:::-;;;;;;;;;;;;;;:34;;;;;;;;;;;;;;;28835:17;:19;;;28783:27;28835:19;;;:::i;:::-;;;;;;28731:138;28566:3;;28526:353;;;;28943:16;28960:6;;28943:24;;;;;;;:::i;:::-;;;;;;;;;;;;;;:26;;;:24;:26;;;:::i;:::-;;;;;;28979:24;28996:6;;28979:24;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;28979:16:34;;-1:-1:-1;;;28979:24:34:i;:::-;29064:9;29059:137;29083:7;:14;29079:1;:18;29059:137;;;29118:10;29129:7;29137:1;29129:10;;;;;;;;:::i;:::-;;;;;;;29118:22;;;;;;:::i;:::-;;;;;;;;;;;;;;:24;;;:22;:24;;;:::i;:::-;;;;;;29156:29;29174:7;29182:1;29174:10;;;;;;;;:::i;:::-;;;;;;;29156:17;:29::i;:::-;29099:3;;29059:137;;;;29247:11;29227:10;29211:57;;;29239:6;;29260:7;29211:57;;;;;;;;:::i;:::-;;;;;;;;27359:1916;;;;:::o;14717:166::-;14787:20;;;;:14;:20;;;;;;;;:29;;;;;;;;;;;:36;;;;14819:4;14787:36;;;14838:38;14865:10;;14802:4;;14838:38;;14787:20;14838:38;14717:166;;:::o;4603:312:20:-;4683:4;4675:23;4692:6;4675:23;;;:120;;;4789:6;4753:42;;:32;811:66:24;1519:53;;;;1441:138;4753:32:20;:42;;;;4675:120;4658:251;;;4869:29;;;;;;;;;;;;;;4658:251;4603:312::o;13550:125:34:-;10709:10;10682:26;:38;;;:26;;:38;:26;:38;;;;;10674:60;;;;;;;;;;;;;;;;;13550:125;:::o;6057:538:20:-;6174:17;6156:50;;;:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;6156:52:20;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;6152:437;;6518:60;;;;;24241:42:38;24229:55;;6518:60:20;;;24211:74:38;24184:18;;6518:60:20;24065:226:38;6152:437:20;811:66:24;6250:40:20;;6246:120;;6317:34;;;;;;;;160:25:38;;;133:18;;6317:34:20;14:177:38;6246:120:20;6379:54;6409:17;6428:4;6379:29;:54::i;:::-;6209:235;6057:538;;:::o;5032:213::-;5106:4;5098:23;5115:6;5098:23;;5094:145;;5199:29;;;;;;;;;;;;;;2970:67;6931:20:19;:18;:20::i;29422:1806:34:-;29488:18;29509:16;29526:5;29509:23;;;;;;:::i;:::-;;;;;;;;;;;;;;;29667:10;:17;29509:23;;-1:-1:-1;29614:17:34;;29591:20;29694:206;29718:15;29714:1;:19;29694:206;;;29809:5;29793:23;;;;;;29774:10;29785:1;29774:13;;;;;;;;:::i;:::-;;;;;;;;29758:31;;;;;;:::i;:::-;;;;;;;;:58;29754:136;;29851:1;29836:16;;29870:5;;29754:136;29735:3;;29694:206;;;;29930:17;29914:12;:33;29910:791;;2232:3;30007:15;:33;30003:688;;;30108:10;:22;;;;;;;-1:-1:-1;30108:22:34;;;;;;;30124:5;30108:22;;:::i;:::-;-1:-1:-1;30148:17:34;;;;:::i;:::-;;-1:-1:-1;30198:19:34;;-1:-1:-1;30216:1:34;30148:17;30198:19;:::i;:::-;30183:34;;30003:688;;;30376:10;30324:16;30341:10;30352:19;30370:1;30352:15;:19;:::i;:::-;30341:31;;;;;;;;:::i;:::-;;;;;;;;30324:49;;;;;;:::i;:::-;;;;;;;;;;;;;;:62;30320:357;;;30486:5;30452:10;30463:19;30481:1;30463:15;:19;:::i;:::-;30452:31;;;;;;;;:::i;:::-;;;;;;;;:39;;;;;;:::i;:::-;-1:-1:-1;30528:19:34;30546:1;30528:15;:19;:::i;30320:357::-;30652:7;;;29422:1806;:::o;30320:357::-;30803:12;30825:127;30847:1;30832:12;:16;:79;;;;-1:-1:-1;30901:10:34;30852:16;30869:10;30880:16;30895:1;30880:12;:16;:::i;:::-;30869:28;;;;;;;;:::i;:::-;;;;;;;;30852:46;;;;;;:::i;:::-;;;;;;;;;;;;;;:59;30832:79;30825:127;;;30927:14;;;;:::i;:::-;;;;30825:127;;;31028:12;31012;:28;31008:214;;31056:18;31077:10;31088:12;31077:24;;;;;;;;:::i;:::-;;;;;;;;31056:45;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;31142:10;31153:12;31142:24;;;;;;;;:::i;:::-;;;;;;;;31115:10;31126:12;31115:24;;;;;;;;:::i;:::-;;;;;;;;:51;;;;;;:::i;:::-;;31207:4;31180:10;31191:12;31180:24;;;;;;;;:::i;:::-;;;;;;;;:31;;;;;;:::i;:::-;;31042:180;31008:214;29478:1750;;;;29422:1806;:::o;31378:1817::-;31446:18;31467:10;31478:6;31467:18;;;;;;:::i;:::-;;;;;;;;;;;;;;;31622:11;:18;31467;;-1:-1:-1;31568:17:34;;31545:20;31650:209;31674:16;31670:1;:20;31650:209;;;31767:6;31751:24;;;;;;31731:11;31743:1;31731:14;;;;;;;;:::i;:::-;;;;;;;;31715:32;;;;;;:::i;:::-;;;;;;;;:60;31711:138;;31810:1;31795:16;;31829:5;;31711:138;31692:3;;31650:209;;;;31889:17;31873:12;:33;31869:799;;2232:3;31967:16;:34;31963:695;;;32069:11;:24;;;;;;;-1:-1:-1;32069:24:34;;;;;;;32086:6;32069:24;;:::i;:::-;-1:-1:-1;32111:18:34;;;;:::i;:::-;;-1:-1:-1;32162:20:34;;-1:-1:-1;32181:1:34;32111:18;32162:20;:::i;:::-;32147:35;;31963:695;;;32338:10;32290;32301:11;32313:20;32332:1;32313:16;:20;:::i;:::-;32301:33;;;;;;;;:::i;:::-;;;;;;;;32290:45;;;;;;:::i;:::-;;;;;;;;;;;;;;:58;32286:358;;;32450:6;32414:11;32426:20;32445:1;32426:16;:20;:::i;:::-;32414:33;;;;;;;;:::i;:::-;;;;;;;;:42;;;;;;:::i;:::-;-1:-1:-1;32493:20:34;32512:1;32493:16;:20;:::i;32286:358::-;32770:12;32792:122;32814:1;32799:12;:16;:74;;;;-1:-1:-1;32863:10:34;32819;32830:11;32842:16;32857:1;32842:12;:16;:::i;:::-;32830:29;;;;;;;;:::i;:::-;;;;;;;;32819:41;;;;;;:::i;:::-;;;;;;;;;;;;;;:54;32799:74;32792:122;;;32889:14;;;;:::i;:::-;;;;32792:122;;;32991:12;32975;:28;32971:218;;33019:18;33040:11;33052:12;33040:25;;;;;;;;:::i;:::-;;;;;;;;33019:46;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;33107:11;33119:12;33107:25;;;;;;;;:::i;:::-;;;;;;;;33079:11;33091:12;33079:25;;;;;;;;:::i;:::-;;;;;;;;:53;;;;;;:::i;:::-;;33174:4;33146:11;33158:12;33146:25;;;;;;;;:::i;2264:344:24:-;2355:37;2374:17;2355:18;:37::i;:::-;2407:36;;;;;;;;;;;2458:11;;:15;2454:148;;2489:53;2518:17;2537:4;2489:28;:53::i;2454:148::-;2573:18;:16;:18::i;7084:141:19:-;8870:21;8560:40;;;;;;7146:73;;7191:17;;;;;;;;;;;;;;1671:281:24;1748:17;:29;;;1781:1;1748:34;1744:119;;1805:47;;;;;24241:42:38;24229:55;;1805:47:24;;;24211:74:38;24184:18;;1805:47:24;24065:226:38;1744:119:24;811:66;1872:73;;;;;;;;;;;;;;;1671:281::o;3916:253:28:-;3999:12;4024;4038:23;4065:6;:19;;4085:4;4065:25;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4023:67;;;;4107:55;4134:6;4142:7;4151:10;4107:26;:55::i;:::-;4100:62;3916:253;-1:-1:-1;;;;;3916:253:28:o;6113:122:24:-;6163:9;:13;6159:70;;6199:19;;;;;;;;;;;;;;4437:582:28;4581:12;4610:7;4605:408;;4633:19;4641:10;4633:7;:19::i;:::-;4605:408;;;4857:17;;:22;:49;;;;-1:-1:-1;4883:18:28;;;;:23;4857:49;4853:119;;;4933:24;;;;;24241:42:38;24229:55;;4933:24:28;;;24211:74:38;24184:18;;4933:24:28;24065:226:38;4853:119:28;-1:-1:-1;4992:10:28;4985:17;;5559:487;5690:17;;:21;5686:354;;5887:10;5881:17;5943:15;5930:10;5926:2;5922:19;5915:44;5686:354;6010:19;;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;:::i;:::-;;;:::o;:::-;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;196:367:38;259:8;269:6;323:3;316:4;308:6;304:17;300:27;290:55;;341:1;338;331:12;290:55;-1:-1:-1;364:20:38;;407:18;396:30;;393:50;;;439:1;436;429:12;393:50;476:4;468:6;464:17;452:29;;536:3;529:4;519:6;516:1;512:14;504:6;500:27;496:38;493:47;490:67;;;553:1;550;543:12;568:671;672:6;680;688;696;749:2;737:9;728:7;724:23;720:32;717:52;;;765:1;762;755:12;717:52;810:23;;;-1:-1:-1;930:2:38;915:18;;902:32;;-1:-1:-1;1011:2:38;996:18;;983:32;1038:18;1027:30;;1024:50;;;1070:1;1067;1060:12;1024:50;1109:70;1171:7;1162:6;1151:9;1147:22;1109:70;:::i;:::-;568:671;;;;-1:-1:-1;1198:8:38;-1:-1:-1;;;;568:671:38:o;1244:609::-;1432:2;1444:21;;;1514:13;;1417:18;;;1536:22;;;1384:4;;1615:15;;;1589:2;1574:18;;;1384:4;1658:169;1672:6;1669:1;1666:13;1658:169;;;1733:13;;1721:26;;1776:2;1802:15;;;;1767:12;;;;1694:1;1687:9;1658:169;;;-1:-1:-1;1844:3:38;;1244:609;-1:-1:-1;;;;;1244:609:38:o;1858:348::-;1910:8;1920:6;1974:3;1967:4;1959:6;1955:17;1951:27;1941:55;;1992:1;1989;1982:12;1941:55;-1:-1:-1;2015:20:38;;2058:18;2047:30;;2044:50;;;2090:1;2087;2080:12;2044:50;2127:4;2119:6;2115:17;2103:29;;2179:3;2172:4;2163:6;2155;2151:19;2147:30;2144:39;2141:59;;;2196:1;2193;2186:12;2211:411;2282:6;2290;2343:2;2331:9;2322:7;2318:23;2314:32;2311:52;;;2359:1;2356;2349:12;2311:52;2399:9;2386:23;2432:18;2424:6;2421:30;2418:50;;;2464:1;2461;2454:12;2418:50;2503:59;2554:7;2545:6;2534:9;2530:22;2503:59;:::i;:::-;2581:8;;2477:85;;-1:-1:-1;2211:411:38;-1:-1:-1;;;;2211:411:38:o;2809:346::-;2877:6;2885;2938:2;2926:9;2917:7;2913:23;2909:32;2906:52;;;2954:1;2951;2944:12;2906:52;-1:-1:-1;;2999:23:38;;;3119:2;3104:18;;;3091:32;;-1:-1:-1;2809:346:38:o;3160:359::-;3213:3;3251:5;3245:12;3278:6;3273:3;3266:19;3334:6;3327:4;3320:5;3316:16;3309:4;3304:3;3300:14;3294:47;3386:1;3379:4;3370:6;3365:3;3361:16;3357:27;3350:38;3508:4;3438:66;3433:2;3425:6;3421:15;3417:88;3412:3;3408:98;3404:109;3397:116;;;3160:359;;;;:::o;3524:649::-;3576:3;3607;3639:5;3633:12;3666:6;3661:3;3654:19;3698:4;3693:3;3689:14;3682:21;;3756:4;3746:6;3743:1;3739:14;3732:5;3728:26;3724:37;3795:4;3788:5;3784:16;3818:1;3828:319;3842:6;3839:1;3836:13;3828:319;;;3925:66;3917:5;3911:4;3907:16;3903:89;3898:3;3891:102;4014:49;4058:4;4049:6;4043:13;4014:49;:::i;:::-;4098:4;4123:14;;;;4006:57;;-1:-1:-1;4086:17:38;;;;;3864:1;3857:9;3828:319;;;-1:-1:-1;4163:4:38;;3524:649;-1:-1:-1;;;;;;3524:649:38:o;4178:802::-;4455:2;4444:9;4437:21;4418:4;4481:55;4532:2;4521:9;4517:18;4509:6;4481:55;:::i;:::-;4584:9;4576:6;4572:22;4567:2;4556:9;4552:18;4545:50;4615:6;4650;4644:13;4681:6;4673;4666:22;4716:2;4708:6;4704:15;4697:22;;4754:2;4746:6;4742:15;4728:29;;4775:1;4785:169;4799:6;4796:1;4793:13;4785:169;;;4860:13;;4848:26;;4903:2;4929:15;;;;4894:12;;;;4821:1;4814:9;4785:169;;4985:525;5065:6;5073;5081;5134:2;5122:9;5113:7;5109:23;5105:32;5102:52;;;5150:1;5147;5140:12;5102:52;5195:23;;;-1:-1:-1;5293:2:38;5278:18;;5265:32;5320:18;5309:30;;5306:50;;;5352:1;5349;5342:12;5306:50;5391:59;5442:7;5433:6;5422:9;5418:22;5391:59;:::i;:::-;4985:525;;5469:8;;-1:-1:-1;5365:85:38;;-1:-1:-1;;;;4985:525:38:o;5515:280::-;5714:2;5703:9;5696:21;5677:4;5734:55;5785:2;5774:9;5770:18;5762:6;5734:55;:::i;5800:196::-;5868:20;;5928:42;5917:54;;5907:65;;5897:93;;5986:1;5983;5976:12;5897:93;5800:196;;;:::o;6001:254::-;6069:6;6077;6130:2;6118:9;6109:7;6105:23;6101:32;6098:52;;;6146:1;6143;6136:12;6098:52;6182:9;6169:23;6159:33;;6211:38;6245:2;6234:9;6230:18;6211:38;:::i;:::-;6201:48;;6001:254;;;;;:::o;6260:184::-;6312:77;6309:1;6302:88;6409:4;6406:1;6399:15;6433:4;6430:1;6423:15;6449:334;6520:2;6514:9;6576:2;6566:13;;6581:66;6562:86;6550:99;;6679:18;6664:34;;6700:22;;;6661:62;6658:88;;;6726:18;;:::i;:::-;6762:2;6755:22;6449:334;;-1:-1:-1;6449:334:38:o;6788:508::-;6852:5;6884:1;6908:18;6900:6;6897:30;6894:56;;;6930:18;;:::i;:::-;-1:-1:-1;6987:2:38;6975:15;;6992:66;6971:88;7061:4;6967:99;7084:21;6967:99;7084:21;:::i;:::-;7075:30;;;7128:6;7121:5;7114:21;7168:3;7159:6;7154:3;7150:16;7147:25;7144:45;;;7185:1;7182;7175:12;7144:45;7234:6;7229:3;7222:4;7215:5;7211:16;7198:43;7288:1;7281:4;7272:6;7265:5;7261:18;7257:29;7250:40;6788:508;;;;;:::o;7301:523::-;7378:6;7386;7439:2;7427:9;7418:7;7414:23;7410:32;7407:52;;;7455:1;7452;7445:12;7407:52;7478:29;7497:9;7478:29;:::i;:::-;7468:39;;7558:2;7547:9;7543:18;7530:32;7585:18;7577:6;7574:30;7571:50;;;7617:1;7614;7607:12;7571:50;7640:22;;7693:4;7685:13;;7681:27;-1:-1:-1;7671:55:38;;7722:1;7719;7712:12;7671:55;7745:73;7810:7;7805:2;7792:16;7787:2;7783;7779:11;7745:73;:::i;:::-;7735:83;;;7301:523;;;;;:::o;7829:226::-;7888:6;7941:2;7929:9;7920:7;7916:23;7912:32;7909:52;;;7957:1;7954;7947:12;7909:52;-1:-1:-1;8002:23:38;;7829:226;-1:-1:-1;7829:226:38:o;8060:713::-;8157:6;8165;8173;8181;8189;8242:3;8230:9;8221:7;8217:23;8213:33;8210:53;;;8259:1;8256;8249:12;8210:53;8304:23;;;-1:-1:-1;8424:2:38;8409:18;;8396:32;;-1:-1:-1;8501:2:38;8486:18;;8473:32;;-1:-1:-1;8556:2:38;8541:18;;8528:32;8583:18;8572:30;;8569:50;;;8615:1;8612;8605:12;8569:50;8654:59;8705:7;8696:6;8685:9;8681:22;8654:59;:::i;:::-;8060:713;;;;-1:-1:-1;8060:713:38;;-1:-1:-1;8732:8:38;;8628:85;8060:713;-1:-1:-1;;;8060:713:38:o;8778:449::-;8876:6;8884;8937:2;8925:9;8916:7;8912:23;8908:32;8905:52;;;8953:1;8950;8943:12;8905:52;8993:9;8980:23;9026:18;9018:6;9015:30;9012:50;;;9058:1;9055;9048:12;9012:50;9097:70;9159:7;9150:6;9139:9;9135:22;9097:70;:::i;9424:420::-;9501:6;9509;9517;9570:2;9558:9;9549:7;9545:23;9541:32;9538:52;;;9586:1;9583;9576:12;9538:52;9631:23;;;-1:-1:-1;9751:2:38;9736:18;;9723:32;;-1:-1:-1;9800:38:38;9834:2;9819:18;;9800:38;:::i;:::-;9790:48;;9424:420;;;;;:::o;9849:660::-;10039:2;10051:21;;;10121:13;;10024:18;;;10143:22;;;9991:4;;10222:15;;;10196:2;10181:18;;;9991:4;10265:218;10279:6;10276:1;10273:13;10265:218;;;10344:13;;10359:42;10340:62;10328:75;;10432:2;10458:15;;;;10423:12;;;;10301:1;10294:9;10265:218;;10514:231;10663:2;10652:9;10645:21;10626:4;10683:56;10735:2;10724:9;10720:18;10712:6;10683:56;:::i;11192:1546::-;11404:4;11452:2;11441:9;11437:18;11482:2;11471:9;11464:21;11505:6;11540;11534:13;11571:6;11563;11556:22;11609:2;11598:9;11594:18;11587:25;;11671:2;11661:6;11658:1;11654:14;11643:9;11639:30;11635:39;11621:53;;11709:2;11701:6;11697:15;11730:1;11740:969;11754:6;11751:1;11748:13;11740:969;;;11819:22;;;11843:66;11815:95;11803:108;;11934:13;;12008:9;;12030:24;;;12088:2;12185:11;;;;12076:15;;;;12008:9;12138:1;12134:16;;;12122:29;;12118:38;12220:1;12234:366;12250:8;12245:3;12242:17;12234:366;;;12352:66;12343:6;12335;12331:19;12327:92;12320:5;12313:107;12447:53;12493:6;12482:8;12476:15;12447:53;:::i;:::-;12543:2;12529:17;;;;12572:14;;;;;12437:63;-1:-1:-1;12278:1:38;12269:11;12234:366;;;-1:-1:-1;12623:6:38;-1:-1:-1;;;12664:2:38;12687:12;;;;12652:15;;;;;-1:-1:-1;11776:1:38;11769:9;11740:969;;;-1:-1:-1;12726:6:38;;11192:1546;-1:-1:-1;;;;;;11192:1546:38:o;12743:186::-;12802:6;12855:2;12843:9;12834:7;12830:23;12826:32;12823:52;;;12871:1;12868;12861:12;12823:52;12894:29;12913:9;12894:29;:::i;13187:1656::-;13311:6;13319;13327;13335;13388:2;13376:9;13367:7;13363:23;13359:32;13356:52;;;13404:1;13401;13394:12;13356:52;13449:23;;;-1:-1:-1;13547:2:38;13532:18;;13519:32;13574:18;13563:30;;13560:50;;;13606:1;13603;13596:12;13560:50;13629:22;;13682:4;13674:13;;13670:27;-1:-1:-1;13660:55:38;;13711:1;13708;13701:12;13660:55;13751:2;13738:16;13777:18;13769:6;13766:30;13763:56;;;13799:18;;:::i;:::-;13845:6;13842:1;13838:14;13872:28;13896:2;13892;13888:11;13872:28;:::i;:::-;13934:19;;;13978:2;14008:11;;;14004:20;;;13969:12;;;;14036:19;;;14033:39;;;14068:1;14065;14058:12;14033:39;14100:2;14096;14092:11;14081:22;;14112:433;14128:6;14123:3;14120:15;14112:433;;;14214:3;14201:17;14250:18;14237:11;14234:35;14231:55;;;14282:1;14279;14272:12;14231:55;14309:20;;14364:2;14356:11;;14352:25;-1:-1:-1;14342:53:38;;14391:1;14388;14381:12;14342:53;14420:82;14494:7;14488:2;14484;14480:11;14467:25;14462:2;14458;14454:11;14420:82;:::i;:::-;14408:95;;-1:-1:-1;14532:2:38;14145:12;;;;14523;;;;14112:433;;;14564:5;-1:-1:-1;;;;14622:2:38;14607:18;;14594:32;;-1:-1:-1;14651:18:38;14638:32;;14635:52;;;14683:1;14680;14673:12;14635:52;14722:61;14775:7;14764:8;14753:9;14749:24;14722:61;:::i;14848:184::-;14900:77;14897:1;14890:88;14997:4;14994:1;14987:15;15021:4;15018:1;15011:15;15037:273;15222:6;15214;15209:3;15196:33;15178:3;15248:16;;15273:13;;;15248:16;15037:273;-1:-1:-1;15037:273:38:o;15735:184::-;15787:77;15784:1;15777:88;15884:4;15881:1;15874:15;15908:4;15905:1;15898:15;15924:128;15991:9;;;16012:11;;;16009:37;;;16026:18;;:::i;16057:437::-;16136:1;16132:12;;;;16179;;;16200:61;;16254:4;16246:6;16242:17;16232:27;;16200:61;16307:2;16299:6;16296:14;16276:18;16273:38;16270:218;;16344:77;16341:1;16334:88;16445:4;16442:1;16435:15;16473:4;16470:1;16463:15;16270:218;;16057:437;;;:::o;16499:212::-;16541:3;16579:5;16573:12;16623:6;16616:4;16609:5;16605:16;16600:3;16594:36;16685:1;16649:16;;16674:13;;;-1:-1:-1;16649:16:38;;16499:212;-1:-1:-1;16499:212:38:o;16716:192::-;16847:3;16872:30;16898:3;16890:6;16872:30;:::i;17039:518::-;17141:2;17136:3;17133:11;17130:421;;;17177:5;17174:1;17167:16;17221:4;17218:1;17208:18;17291:2;17279:10;17275:19;17272:1;17268:27;17262:4;17258:38;17327:4;17315:10;17312:20;17309:47;;;-1:-1:-1;17350:4:38;17309:47;17405:2;17400:3;17396:12;17393:1;17389:20;17383:4;17379:31;17369:41;;17460:81;17478:2;17471:5;17468:13;17460:81;;;17537:1;17523:16;;17504:1;17493:13;17460:81;;17793:1317;17917:18;17912:3;17909:27;17906:53;;;17939:18;;:::i;:::-;17968:94;18058:3;18018:38;18050:4;18044:11;18018:38;:::i;:::-;18012:4;17968:94;:::i;:::-;18088:1;18113:2;18108:3;18105:11;18130:1;18125:727;;;;18896:1;18913:3;18910:93;;;-1:-1:-1;18969:19:38;;;18956:33;18910:93;17699:66;17690:1;17686:11;;;17682:84;17678:89;17668:100;17774:1;17770:11;;;17665:117;19016:78;;18098:1006;;18125:727;16986:1;16979:14;;;17023:4;17010:18;;18170:66;18161:76;;;18335:229;18349:7;18346:1;18343:14;18335:229;;;18438:19;;;18425:33;18410:49;;18545:4;18530:20;;;;18498:1;18486:14;;;;18365:12;18335:229;;;18339:3;18592;18583:7;18580:16;18577:219;;;18712:66;18706:3;18700;18697:1;18693:11;18689:21;18685:94;18681:99;18668:9;18663:3;18659:19;18646:33;18642:139;18634:6;18627:155;18577:219;;;18839:1;18833:3;18830:1;18826:11;18822:19;18816:4;18809:33;18098:1006;;17793:1317;;;:::o;19115:326::-;19204:6;19199:3;19192:19;19256:6;19249:5;19242:4;19237:3;19233:14;19220:43;;19308:1;19301:4;19292:6;19287:3;19283:16;19279:27;19272:38;19174:3;19430:4;19360:66;19355:2;19347:6;19343:15;19339:88;19334:3;19330:98;19326:109;19319:116;;19115:326;;;;:::o;19446:247::-;19605:2;19594:9;19587:21;19568:4;19625:62;19683:2;19672:9;19668:18;19660:6;19652;19625:62;:::i;:::-;19617:70;19446:247;-1:-1:-1;;;;19446:247:38:o;19698:1516::-;19817:3;19811:4;19808:13;19805:26;;19824:5;;19698:1516::o;19805:26::-;19854:37;19886:3;19880:10;19854:37;:::i;:::-;19914:18;19906:6;19903:30;19900:56;;;19936:18;;:::i;:::-;19965:97;20055:6;20015:38;20047:4;20041:11;20015:38;:::i;:::-;20009:4;19965:97;:::i;:::-;20088:1;20116:2;20108:6;20105:14;20133:1;20128:829;;;;21001:1;21018:6;21015:89;;;-1:-1:-1;21070:19:38;;;21064:26;21015:89;17774:1;17770:11;;;17699:66;17690:1;17686:11;;;17682:84;17678:89;17668:100;;17665:117;21130:67;21124:4;21117:81;;20098:1110;;20128:829;16986:1;16979:14;;;17023:4;17010:18;;;16979:14;;;17010:18;;;20176:66;20164:79;;;20401:221;20415:7;20412:1;20409:14;20401:221;;;20497:21;;;20491:28;20476:44;;20559:1;20591:17;;;;20547:14;;;;20438:4;20431:12;20401:221;;;20405:3;20650:6;20641:7;20638:19;20635:263;;;20711:21;;;20705:28;20814:66;20796:1;20792:14;;;20808:3;20788:24;20784:97;20780:102;20765:118;20750:134;;20635:263;-1:-1:-1;;;;;20944:1:38;20928:14;;;20924:22;20911:36;;-1:-1:-1;19698:1516:38:o;21219:184::-;21271:77;21268:1;21261:88;21368:4;21365:1;21358:15;21392:4;21389:1;21382:15;21408:216;21472:9;;;21500:11;;;21447:3;21530:9;;21558:10;;21554:19;;21583:10;;21575:19;;21551:44;21548:70;;;21598:18;;:::i;:::-;21548:70;;21408:216;;;;:::o;21629:316::-;21814:6;21803:9;21796:25;21857:2;21852;21841:9;21837:18;21830:30;21777:4;21877:62;21935:2;21924:9;21920:18;21912:6;21904;21877:62;:::i;21950:316::-;22135:2;22124:9;22117:21;22098:4;22155:62;22213:2;22202:9;22198:18;22190:6;22182;22155:62;:::i;:::-;22147:70;;22253:6;22248:2;22237:9;22233:18;22226:34;21950:316;;;;;;:::o;22271:581::-;22349:4;22355:6;22415:11;22402:25;22505:66;22494:8;22478:14;22474:29;22470:102;22450:18;22446:127;22436:155;;22587:1;22584;22577:12;22436:155;22614:33;;22666:20;;;-1:-1:-1;22709:18:38;22698:30;;22695:50;;;22741:1;22738;22731:12;22695:50;22774:4;22762:17;;-1:-1:-1;22805:14:38;22801:27;;;22791:38;;22788:58;;;22842:1;22839;22832:12;23071:125;23136:9;;;23157:10;;;23154:36;;;23170:18;;:::i;23201:195::-;23240:3;23271:66;23264:5;23261:77;23258:103;;23341:18;;:::i;:::-;-1:-1:-1;23388:1:38;23377:13;;23201:195::o;23401:470::-;23658:2;23647:9;23640:21;23621:4;23684:62;23742:2;23731:9;23727:18;23719:6;23711;23684:62;:::i;:::-;23794:9;23786:6;23782:22;23777:2;23766:9;23762:18;23755:50;23822:43;23858:6;23850;23822:43;:::i;:::-;23814:51;23401:470;-1:-1:-1;;;;;;23401:470:38:o;23876:184::-;23946:6;23999:2;23987:9;23978:7;23974:23;23970:32;23967:52;;;24015:1;24012;24005:12;23967:52;-1:-1:-1;24038:16:38;;23876:184;-1:-1:-1;23876:184:38:o;24296:738::-;24349:3;24390:5;24384:12;24419:36;24445:9;24419:36;:::i;:::-;24486:1;24471:17;;24497:191;;;;24702:1;24697:331;;;;24464:564;;24497:191;24545:66;24534:9;24530:82;24525:3;24518:95;24668:6;24661:14;24654:22;24646:6;24642:35;24637:3;24633:45;24626:52;;24497:191;;24697:331;24728:5;24725:1;24718:16;24775:4;24772:1;24762:18;24802:1;24816:166;24830:6;24827:1;24824:13;24816:166;;;24910:14;;24897:11;;;24890:35;24966:1;24953:15;;;;24852:4;24845:12;24816:166;;;24820:3;;25011:6;25006:3;25002:16;24995:23;;24464:564;;;;24296:738;;;;:::o;25039:202::-;25169:3;25194:41;25231:3;25223:6;25194:41;:::i;25246:1418::-;25372:3;25366:10;25399:18;25391:6;25388:30;25385:56;;;25421:18;;:::i;:::-;25450:97;25540:6;25500:38;25532:4;25526:11;25500:38;:::i;25450:97::-;25596:4;25627:2;25616:14;;25644:1;25639:768;;;;26451:1;26468:6;26465:89;;;-1:-1:-1;26520:19:38;;;26514:26;17774:1;17770:11;;;17699:66;17690:1;17686:11;;;17682:84;17678:89;17668:100;;17665:117;26580:67;17562:226;25639:768;16986:1;16979:14;;;17023:4;17010:18;;25687:66;25675:79;;;25852:222;25866:7;25863:1;25860:14;25852:222;;;25948:19;;;25942:26;25927:42;;26055:4;26040:20;;;;26008:1;25996:14;;;;25882:12;25852:222;;;25856:3;26102:6;26093:7;26090:19;26087:261;;;26163:19;;;26157:26;26264:66;26246:1;26242:14;;;26258:3;26238:24;26234:97;26230:102;26215:118;26200:134;;26087:261;-1:-1:-1;;;;26394:1:38;26378:14;;;26374:22;26361:36;;-1:-1:-1;25246:1418:38:o;26874:196::-;26913:3;26941:5;26931:39;;26950:18;;:::i;:::-;-1:-1:-1;26997:66:38;26986:78;;26874:196::o", "linkReferences": {}, "immutableReferences": {"39359": [{"start": 9252, "length": 32}, {"start": 9293, "length": 32}, {"start": 9938, "length": 32}]}}, "methodIdentifiers": {"BOOTNODE_MANAGER_ROLE()": "7c8973c7", "OWNER_ROLE()": "e58378bb", "STAGE_MANAGER_ROLE()": "068dc322", "UPGRADE_INTERFACE_VERSION()": "ad3cb1cc", "addBootnodes(string[])": "d90d8573", "clearBootnodes()": "4179a759", "currentRound()": "8a19c8bc", "currentStage()": "5bf5d54c", "getBootnodes()": "6370ae4f", "getBootnodesCount()": "48495bdb", "getEoa(string[])": "96bac35a", "getPeerId(address[])": "b894a469", "getPeerVoteCount(uint256,string)": "4f4026c3", "getRoundStageReward(uint256,uint256,address[])": "098f027f", "getTotalRewards(string[])": "80c3d97f", "getTotalWins(string)": "099c4002", "getVoterVoteCount(string)": "dfb3c7df", "getVoterVotes(uint256,string)": "2bdd8ea6", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "hasSubmittedRoundStageReward(uint256,uint256,address)": "9291fee5", "initialize(address)": "c4d66de8", "proxiableUUID()": "52d1902d", "registerPeer(string)": "33e7fb45", "removeBootnode(uint256)": "4f52ca36", "revokeRole(bytes32,address)": "d547741f", "stageCount()": "f33261ac", "submitReward(uint256,uint256,int256,string)": "5194e15f", "submitWinners(uint256,string[],string)": "fbe94d68", "uniqueVotedPeers()": "42d2c6a0", "uniqueVoters()": "b0c77404", "updateStageAndRound()": "e28b0586", "upgradeToAndCall(address,bytes)": "4f1ef286", "voterLeaderboard(uint256,uint256)": "18a6fd88", "winnerLeaderboard(uint256,uint256)": "2f4be652"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"ERC1967InvalidImplementation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ERC1967NonPayable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidBootnodeIndex\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidRoundNumber\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidStageNumber\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidVote\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidVoterPeerId\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyBootnodeManager\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyOwner\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyStageManager\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PeerIdAlreadyRegistered\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RewardAlreadySubmitted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"StageOutOfBounds\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UUPSUnauthorizedCallContext\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"slot\",\"type\":\"bytes32\"}],\"name\":\"UUPSUnsupportedProxiableUUID\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"WinnerAlreadyVoted\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"manager\",\"type\":\"address\"}],\"name\":\"AllBootnodesCleared\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"manager\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"BootnodeRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"manager\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"count\",\"type\":\"uint256\"}],\"name\":\"BootnodesAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"peerId\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"totalRewards\",\"type\":\"int256\"}],\"name\":\"CumulativeRewardsUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"eoa\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"peerId\",\"type\":\"string\"}],\"name\":\"PeerRegistered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"roundNumber\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"stageNumber\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"reward\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"peerId\",\"type\":\"string\"}],\"name\":\"RewardSubmitted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"newRoundNumber\",\"type\":\"uint256\"}],\"name\":\"RoundAdvanced\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"roundNumber\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newStage\",\"type\":\"uint256\"}],\"name\":\"StageAdvanced\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"peerId\",\"type\":\"string\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"roundNumber\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string[]\",\"name\":\"winners\",\"type\":\"string[]\"}],\"name\":\"WinnerSubmitted\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"BOOTNODE_MANAGER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"OWNER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"STAGE_MANAGER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"UPGRADE_INTERFACE_VERSION\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"newBootnodes\",\"type\":\"string[]\"}],\"name\":\"addBootnodes\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"clearBootnodes\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"currentRound\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"currentStage\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBootnodes\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBootnodesCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"peerIds\",\"type\":\"string[]\"}],\"name\":\"getEoa\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"eoas\",\"type\":\"address[]\"}],\"name\":\"getPeerId\",\"outputs\":[{\"internalType\":\"string[][]\",\"name\":\"\",\"type\":\"string[][]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"roundNumber\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"peerId\",\"type\":\"string\"}],\"name\":\"getPeerVoteCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"roundNumber\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"stageNumber\",\"type\":\"uint256\"},{\"internalType\":\"address[]\",\"name\":\"accounts\",\"type\":\"address[]\"}],\"name\":\"getRoundStageReward\",\"outputs\":[{\"internalType\":\"int256[]\",\"name\":\"\",\"type\":\"int256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"peerIds\",\"type\":\"string[]\"}],\"name\":\"getTotalRewards\",\"outputs\":[{\"internalType\":\"int256[]\",\"name\":\"\",\"type\":\"int256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"peerId\",\"type\":\"string\"}],\"name\":\"getTotalWins\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"peerId\",\"type\":\"string\"}],\"name\":\"getVoterVoteCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"roundNumber\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"peerId\",\"type\":\"string\"}],\"name\":\"getVoterVotes\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"roundNumber\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"stageNumber\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasSubmittedRoundStageReward\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner_\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proxiableUUID\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"peerId\",\"type\":\"string\"}],\"name\":\"registerPeer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"removeBootnode\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"stageCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"roundNumber\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"stageNumber\",\"type\":\"uint256\"},{\"internalType\":\"int256\",\"name\":\"reward\",\"type\":\"int256\"},{\"internalType\":\"string\",\"name\":\"peerId\",\"type\":\"string\"}],\"name\":\"submitReward\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"roundNumber\",\"type\":\"uint256\"},{\"internalType\":\"string[]\",\"name\":\"winners\",\"type\":\"string[]\"},{\"internalType\":\"string\",\"name\":\"peerId\",\"type\":\"string\"}],\"name\":\"submitWinners\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"uniqueVotedPeers\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"uniqueVoters\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"updateStageAndRound\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"upgradeToAndCall\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"start\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"end\",\"type\":\"uint256\"}],\"name\":\"voterLeaderboard\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"peerIds\",\"type\":\"string[]\"},{\"internalType\":\"uint256[]\",\"name\":\"voteCounts\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"start\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"end\",\"type\":\"uint256\"}],\"name\":\"winnerLeaderboard\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"peerIds\",\"type\":\"string[]\"},{\"internalType\":\"uint256[]\",\"name\":\"wins\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Manages coordination of a swarm network including round/stage progression, peer registration, bootnode management, and winner selection.\",\"errors\":{\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"ERC1967InvalidImplementation(address)\":[{\"details\":\"The `implementation` of the proxy is invalid.\"}],\"ERC1967NonPayable()\":[{\"details\":\"An upgrade function sees `msg.value > 0` that may be lost.\"}],\"FailedCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"UUPSUnauthorizedCallContext()\":[{\"details\":\"The call is from an unauthorized context.\"}],\"UUPSUnsupportedProxiableUUID(bytes32)\":[{\"details\":\"The storage `slot` is unsupported as a UUID.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"Upgraded(address)\":{\"details\":\"Emitted when the implementation is upgraded.\"}},\"kind\":\"dev\",\"methods\":{\"addBootnodes(string[])\":{\"details\":\"Adds multiple bootnodes to the list\",\"params\":{\"newBootnodes\":\"Array of bootnode strings to add\"}},\"clearBootnodes()\":{\"details\":\"Clears all bootnodes from the list\"},\"currentRound()\":{\"details\":\"Returns the current round number\",\"returns\":{\"_0\":\"Current round number\"}},\"currentStage()\":{\"details\":\"Returns the current stage number within the round\",\"returns\":{\"_0\":\"Current stage number\"}},\"getBootnodes()\":{\"details\":\"Returns all registered bootnodes\",\"returns\":{\"_0\":\"Array of all bootnode strings\"}},\"getBootnodesCount()\":{\"details\":\"Returns the number of registered bootnodes\",\"returns\":{\"_0\":\"The count of bootnodes\"}},\"getEoa(string[])\":{\"details\":\"Retrieves the EOA addresses associated with multiple peer IDs\",\"params\":{\"peerIds\":\"Array of peer IDs to look up\"},\"returns\":{\"_0\":\"Array of EOA addresses associated with the peer IDs\"}},\"getPeerId(address[])\":{\"details\":\"Retrieves the peer IDs associated with multiple EOA addresses\",\"params\":{\"eoas\":\"Array of EOA addresses to look up\"},\"returns\":{\"_0\":\"Array of peer IDs associated with the EOA addresses\"}},\"getPeerVoteCount(uint256,string)\":{\"details\":\"Gets the vote count for a specific peer ID in a round\",\"params\":{\"peerId\":\"The peer ID to query\",\"roundNumber\":\"The round number to query\"},\"returns\":{\"_0\":\"The number of votes received by the peer ID in that round\"}},\"getRoundStageReward(uint256,uint256,address[])\":{\"details\":\"Gets the reward submitted by accounts for a specific round and stage\",\"params\":{\"accounts\":\"Array of addresses to query\",\"roundNumber\":\"The round number to query\",\"stageNumber\":\"The stage number to query\"},\"returns\":{\"_0\":\"rewards Array of corresponding reward amounts for each account\"}},\"getTotalRewards(string[])\":{\"details\":\"Gets the total rewards earned by accounts across all rounds\",\"params\":{\"peerIds\":\"Array of peer IDs to query\"},\"returns\":{\"_0\":\"rewards Array of corresponding total rewards for each peer ID\"}},\"getTotalWins(string)\":{\"details\":\"Gets the total number of wins for a peer ID\",\"params\":{\"peerId\":\"The peer ID to query\"},\"returns\":{\"_0\":\"The total number of wins for the peer ID\"}},\"getVoterVoteCount(string)\":{\"details\":\"Gets the number of times a voter has voted\",\"params\":{\"peerId\":\"The peer ID of the voter\"},\"returns\":{\"_0\":\"The number of times the voter has voted\"}},\"getVoterVotes(uint256,string)\":{\"details\":\"Gets the votes for a specific round from a specific peer ID\",\"params\":{\"peerId\":\"The peer ID of the voter\",\"roundNumber\":\"The round number to query\"},\"returns\":{\"_0\":\"Array of peer IDs that the voter voted for\"}},\"grantRole(bytes32,address)\":{\"details\":\"Grants a role to an account\",\"params\":{\"account\":\"The address of the account to grant the role to\",\"role\":\"The role to grant\"}},\"hasRole(bytes32,address)\":{\"details\":\"Checks if an account has a role\",\"params\":{\"account\":\"The address of the account to check\",\"role\":\"The role to check\"},\"returns\":{\"_0\":\"True if the account has the role, false otherwise\"}},\"hasSubmittedRoundStageReward(uint256,uint256,address)\":{\"details\":\"Checks if an account has submitted a reward for a specific round and stage\",\"params\":{\"account\":\"The address of the account\",\"roundNumber\":\"The round number to check\",\"stageNumber\":\"The stage number to check\"},\"returns\":{\"_0\":\"True if the account has submitted a reward for that round and stage, false otherwise\"}},\"proxiableUUID()\":{\"details\":\"Implementation of the ERC-1822 {proxiableUUID} function. This returns the storage slot used by the implementation. It is used to validate the implementation's compatibility when performing an upgrade. IMPORTANT: A proxy pointing at a proxiable contract should not be considered proxiable itself, because this risks bricking a proxy that upgrades to it, by delegating to itself until out of gas. Thus it is critical that this function revert if invoked through a proxy. This is guaranteed by the `notDelegated` modifier.\"},\"registerPeer(string)\":{\"details\":\"Registers a peer's ID and associates it with the sender's address\",\"params\":{\"peerId\":\"The peer ID to register\"}},\"removeBootnode(uint256)\":{\"details\":\"Removes a bootnode at the specified index\",\"params\":{\"index\":\"The index of the bootnode to remove\"}},\"revokeRole(bytes32,address)\":{\"details\":\"Removes a role from an account\",\"params\":{\"account\":\"The address of the account to revoke the role from\",\"role\":\"The role to revoke\"}},\"stageCount()\":{\"details\":\"Returns the total number of stages in a round\",\"returns\":{\"_0\":\"Number of stages\"}},\"submitReward(uint256,uint256,int256,string)\":{\"details\":\"Submits a reward for a specific round and stage\",\"params\":{\"peerId\":\"The peer ID reporting the rewards\",\"reward\":\"The reward amount to submit (can be positive or negative)\",\"roundNumber\":\"The round number for which to submit the reward\",\"stageNumber\":\"The stage number for which to submit the reward\"}},\"submitWinners(uint256,string[],string)\":{\"details\":\"Submits a list of winners for a specific round\",\"params\":{\"peerId\":\"The peer ID of the voter\",\"roundNumber\":\"The round number for which to submit the winners\",\"winners\":\"The list of peer IDs that should win\"}},\"uniqueVotedPeers()\":{\"details\":\"Gets the total number of unique peers that have been voted on\",\"returns\":{\"_0\":\"The number of unique peers that have received votes\"}},\"uniqueVoters()\":{\"details\":\"Gets the total number of unique voters who have participated\",\"returns\":{\"_0\":\"The number of unique voters\"}},\"updateStageAndRound()\":{\"details\":\"Updates the current stage and round\",\"returns\":{\"_0\":\"The current round and stage after any updates\"}},\"upgradeToAndCall(address,bytes)\":{\"custom:oz-upgrades-unsafe-allow-reachable\":\"delegatecall\",\"details\":\"Upgrade the implementation of the proxy to `newImplementation`, and subsequently execute the function call encoded in `data`. Calls {_authorizeUpgrade}. Emits an {Upgraded} event.\"},\"voterLeaderboard(uint256,uint256)\":{\"details\":\"Gets a slice of the voter leaderboard\",\"params\":{\"end\":\"The ending index (exclusive)\",\"start\":\"The starting index (inclusive)\"},\"returns\":{\"peerIds\":\"Array of peer IDs sorted by number of votes (descending)\",\"voteCounts\":\"Array of corresponding vote counts\"}},\"winnerLeaderboard(uint256,uint256)\":{\"details\":\"Gets a slice of the leaderboard\",\"params\":{\"end\":\"The ending index (exclusive)\",\"start\":\"The starting index (inclusive)\"},\"returns\":{\"peerIds\":\"Array of peer IDs sorted by number of wins (descending)\",\"wins\":\"Array of corresponding win counts\"}}},\"title\":\"SwarmCoordinator\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"addBootnodes(string[])\":{\"notice\":\"Only callable by the bootnode manager\"},\"clearBootnodes()\":{\"notice\":\"Only callable by the bootnode manager\"},\"grantRole(bytes32,address)\":{\"notice\":\"Only callable by the contract owner\"},\"removeBootnode(uint256)\":{\"notice\":\"Only callable by the bootnode manager\"},\"revokeRole(bytes32,address)\":{\"notice\":\"Only callable by the contract owner\"},\"updateStageAndRound()\":{\"notice\":\"Only callable by the stage manager\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/SwarmCoordinator.sol\":\"SwarmCoordinator\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":1000000},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609\",\"dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/UUPSUpgradeable.sol\":{\"keccak256\":\"0xd861907d1168dcaec2a7846edbaed12feb8bad2d6781dba987be01374f90b495\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://12ff809243040419e2fc2aa7ef0aaa60b3e6ebc901553ba1de970ceeef208c4c\",\"dweb:/ipfs/QmX2dwMVNrQAahqVzEx94gqcVB6Z8ovifPYdEfHZzj7aEb\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d\",\"dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol\":{\"keccak256\":\"0xc42facb5094f2f35f066a7155bda23545e39a3156faef3ddc00185544443ba7d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3b36282ab029b46bd082619a308a2ea11c309967b9425b7b7a6eb0b0c1c3196\",\"dweb:/ipfs/QmP2YVfDB2FoREax3vJu7QhDnyYRMw52WPrCD4vdT2kuDA\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70\",\"dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2\"]},\"lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e\",\"dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR\"]},\"lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"src/SwarmCoordinator.sol\":{\"keccak256\":\"0xf7243562501740ec8d08dfa3a4cb635125515debdcc4f8afd36e574d650dca36\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0e2e9681fd012233f6898e84e9fe6cb999cefdd4e8f742d46333e03d40c5c1bb\",\"dweb:/ipfs/QmWHepH2CNbBakxHKYWHiLemb65GciVxkTk4eaebbcz5RW\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "type": "error", "name": "ERC1967InvalidImplementation"}, {"inputs": [], "type": "error", "name": "ERC1967Non<PERSON>ayable"}, {"inputs": [], "type": "error", "name": "FailedCall"}, {"inputs": [], "type": "error", "name": "InvalidBootnodeIndex"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "InvalidRoundNumber"}, {"inputs": [], "type": "error", "name": "InvalidStageNumber"}, {"inputs": [], "type": "error", "name": "InvalidVote"}, {"inputs": [], "type": "error", "name": "InvalidVoterPeerId"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [], "type": "error", "name": "OnlyBootnodeManager"}, {"inputs": [], "type": "error", "name": "Only<PERSON><PERSON>er"}, {"inputs": [], "type": "error", "name": "OnlyStageManager"}, {"inputs": [], "type": "error", "name": "PeerIdAlreadyRegistered"}, {"inputs": [], "type": "error", "name": "RewardAlreadySubmitted"}, {"inputs": [], "type": "error", "name": "StageOutOfBounds"}, {"inputs": [], "type": "error", "name": "UUPSUnauthorizedCallContext"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "type": "error", "name": "UUPSUnsupportedProxiableUUID"}, {"inputs": [], "type": "error", "name": "WinnerAlreadyVoted"}, {"inputs": [{"internalType": "address", "name": "manager", "type": "address", "indexed": true}], "type": "event", "name": "AllBootnodesCleared", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "manager", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "index", "type": "uint256", "indexed": false}], "type": "event", "name": "BootnodeRemoved", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "manager", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "count", "type": "uint256", "indexed": false}], "type": "event", "name": "BootnodesAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "string", "name": "peerId", "type": "string", "indexed": false}, {"internalType": "int256", "name": "totalRewards", "type": "int256", "indexed": false}], "type": "event", "name": "CumulativeRewardsUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "eoa", "type": "address", "indexed": true}, {"internalType": "string", "name": "peerId", "type": "string", "indexed": false}], "type": "event", "name": "PeerRegistered", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "roundNumber", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "stageNumber", "type": "uint256", "indexed": true}, {"internalType": "int256", "name": "reward", "type": "int256", "indexed": false}, {"internalType": "string", "name": "peerId", "type": "string", "indexed": false}], "type": "event", "name": "RewardSubmitted", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleGranted", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleRevoked", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "newRoundNumber", "type": "uint256", "indexed": true}], "type": "event", "name": "RoundAdvanced", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "newStage", "type": "uint256", "indexed": false}], "type": "event", "name": "StageAdvanced", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": true}], "type": "event", "name": "Upgraded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "string", "name": "peerId", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "roundNumber", "type": "uint256", "indexed": true}, {"internalType": "string[]", "name": "winners", "type": "string[]", "indexed": false}], "type": "event", "name": "WinnerSubmitted", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "BOOTNODE_MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "OWNER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "STAGE_MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "string[]", "name": "newBootnodes", "type": "string[]"}], "stateMutability": "nonpayable", "type": "function", "name": "addBootnodes"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "clearBootnodes"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "currentRound", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "currentStage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBootnodes", "outputs": [{"internalType": "string[]", "name": "", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBootnodesCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "string[]", "name": "peerIds", "type": "string[]"}], "stateMutability": "view", "type": "function", "name": "getEoa", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "address[]", "name": "e<PERSON>s", "type": "address[]"}], "stateMutability": "view", "type": "function", "name": "getPeerId", "outputs": [{"internalType": "string[][]", "name": "", "type": "string[][]"}]}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"internalType": "string", "name": "peerId", "type": "string"}], "stateMutability": "view", "type": "function", "name": "getPeerVoteCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "stageNumber", "type": "uint256"}, {"internalType": "address[]", "name": "accounts", "type": "address[]"}], "stateMutability": "view", "type": "function", "name": "getRoundStageReward", "outputs": [{"internalType": "int256[]", "name": "", "type": "int256[]"}]}, {"inputs": [{"internalType": "string[]", "name": "peerIds", "type": "string[]"}], "stateMutability": "view", "type": "function", "name": "getTotalRewards", "outputs": [{"internalType": "int256[]", "name": "", "type": "int256[]"}]}, {"inputs": [{"internalType": "string", "name": "peerId", "type": "string"}], "stateMutability": "view", "type": "function", "name": "getTotalWins", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "peerId", "type": "string"}], "stateMutability": "view", "type": "function", "name": "getVoterVoteCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"internalType": "string", "name": "peerId", "type": "string"}], "stateMutability": "view", "type": "function", "name": "getVoterVotes", "outputs": [{"internalType": "string[]", "name": "", "type": "string[]"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "grantRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "stageNumber", "type": "uint256"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasSubmittedRoundStageReward", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "owner_", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "string", "name": "peerId", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "registerPeer"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "removeBootnode"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "revokeRole"}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "stageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "stageNumber", "type": "uint256"}, {"internalType": "int256", "name": "reward", "type": "int256"}, {"internalType": "string", "name": "peerId", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "submitReward"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"internalType": "string[]", "name": "winners", "type": "string[]"}, {"internalType": "string", "name": "peerId", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "submitWinners"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "uniqueVotedPeers", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "uniqueVoters", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "updateStageAndRound", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "upgradeToAndCall"}, {"inputs": [{"internalType": "uint256", "name": "start", "type": "uint256"}, {"internalType": "uint256", "name": "end", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "voterLeaderboard", "outputs": [{"internalType": "string[]", "name": "peerIds", "type": "string[]"}, {"internalType": "uint256[]", "name": "voteCounts", "type": "uint256[]"}]}, {"inputs": [{"internalType": "uint256", "name": "start", "type": "uint256"}, {"internalType": "uint256", "name": "end", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "winnerLeaderboard", "outputs": [{"internalType": "string[]", "name": "peerIds", "type": "string[]"}, {"internalType": "uint256[]", "name": "wins", "type": "uint256[]"}]}], "devdoc": {"kind": "dev", "methods": {"addBootnodes(string[])": {"details": "Adds multiple bootnodes to the list", "params": {"newBootnodes": "Array of bootnode strings to add"}}, "clearBootnodes()": {"details": "Clears all bootnodes from the list"}, "currentRound()": {"details": "Returns the current round number", "returns": {"_0": "Current round number"}}, "currentStage()": {"details": "Returns the current stage number within the round", "returns": {"_0": "Current stage number"}}, "getBootnodes()": {"details": "Returns all registered bootnodes", "returns": {"_0": "Array of all bootnode strings"}}, "getBootnodesCount()": {"details": "Returns the number of registered bootnodes", "returns": {"_0": "The count of bootnodes"}}, "getEoa(string[])": {"details": "Retrieves the EOA addresses associated with multiple peer IDs", "params": {"peerIds": "Array of peer IDs to look up"}, "returns": {"_0": "Array of EOA addresses associated with the peer IDs"}}, "getPeerId(address[])": {"details": "Retrieves the peer IDs associated with multiple EOA addresses", "params": {"eoas": "Array of EOA addresses to look up"}, "returns": {"_0": "Array of peer IDs associated with the EOA addresses"}}, "getPeerVoteCount(uint256,string)": {"details": "Gets the vote count for a specific peer ID in a round", "params": {"peerId": "The peer ID to query", "roundNumber": "The round number to query"}, "returns": {"_0": "The number of votes received by the peer ID in that round"}}, "getRoundStageReward(uint256,uint256,address[])": {"details": "Gets the reward submitted by accounts for a specific round and stage", "params": {"accounts": "Array of addresses to query", "roundNumber": "The round number to query", "stageNumber": "The stage number to query"}, "returns": {"_0": "rewards Array of corresponding reward amounts for each account"}}, "getTotalRewards(string[])": {"details": "Gets the total rewards earned by accounts across all rounds", "params": {"peerIds": "Array of peer IDs to query"}, "returns": {"_0": "rewards Array of corresponding total rewards for each peer ID"}}, "getTotalWins(string)": {"details": "Gets the total number of wins for a peer ID", "params": {"peerId": "The peer ID to query"}, "returns": {"_0": "The total number of wins for the peer ID"}}, "getVoterVoteCount(string)": {"details": "Gets the number of times a voter has voted", "params": {"peerId": "The peer ID of the voter"}, "returns": {"_0": "The number of times the voter has voted"}}, "getVoterVotes(uint256,string)": {"details": "Gets the votes for a specific round from a specific peer ID", "params": {"peerId": "The peer ID of the voter", "roundNumber": "The round number to query"}, "returns": {"_0": "Array of peer IDs that the voter voted for"}}, "grantRole(bytes32,address)": {"details": "Grants a role to an account", "params": {"account": "The address of the account to grant the role to", "role": "The role to grant"}}, "hasRole(bytes32,address)": {"details": "Checks if an account has a role", "params": {"account": "The address of the account to check", "role": "The role to check"}, "returns": {"_0": "True if the account has the role, false otherwise"}}, "hasSubmittedRoundStageReward(uint256,uint256,address)": {"details": "Checks if an account has submitted a reward for a specific round and stage", "params": {"account": "The address of the account", "roundNumber": "The round number to check", "stageNumber": "The stage number to check"}, "returns": {"_0": "True if the account has submitted a reward for that round and stage, false otherwise"}}, "proxiableUUID()": {"details": "Implementation of the ERC-1822 {proxiableUUID} function. This returns the storage slot used by the implementation. It is used to validate the implementation's compatibility when performing an upgrade. IMPORTANT: A proxy pointing at a proxiable contract should not be considered proxiable itself, because this risks bricking a proxy that upgrades to it, by delegating to itself until out of gas. Thus it is critical that this function revert if invoked through a proxy. This is guaranteed by the `notDelegated` modifier."}, "registerPeer(string)": {"details": "Registers a peer's ID and associates it with the sender's address", "params": {"peerId": "The peer ID to register"}}, "removeBootnode(uint256)": {"details": "Removes a bootnode at the specified index", "params": {"index": "The index of the bootnode to remove"}}, "revokeRole(bytes32,address)": {"details": "Removes a role from an account", "params": {"account": "The address of the account to revoke the role from", "role": "The role to revoke"}}, "stageCount()": {"details": "Returns the total number of stages in a round", "returns": {"_0": "Number of stages"}}, "submitReward(uint256,uint256,int256,string)": {"details": "Submits a reward for a specific round and stage", "params": {"peerId": "The peer ID reporting the rewards", "reward": "The reward amount to submit (can be positive or negative)", "roundNumber": "The round number for which to submit the reward", "stageNumber": "The stage number for which to submit the reward"}}, "submitWinners(uint256,string[],string)": {"details": "Submits a list of winners for a specific round", "params": {"peerId": "The peer ID of the voter", "roundNumber": "The round number for which to submit the winners", "winners": "The list of peer IDs that should win"}}, "uniqueVotedPeers()": {"details": "Gets the total number of unique peers that have been voted on", "returns": {"_0": "The number of unique peers that have received votes"}}, "uniqueVoters()": {"details": "Gets the total number of unique voters who have participated", "returns": {"_0": "The number of unique voters"}}, "updateStageAndRound()": {"details": "Updates the current stage and round", "returns": {"_0": "The current round and stage after any updates"}}, "upgradeToAndCall(address,bytes)": {"custom:oz-upgrades-unsafe-allow-reachable": "delegatecall", "details": "Upgrade the implementation of the proxy to `newImplementation`, and subsequently execute the function call encoded in `data`. Calls {_authorizeUpgrade}. Emits an {Upgraded} event."}, "voterLeaderboard(uint256,uint256)": {"details": "Gets a slice of the voter leaderboard", "params": {"end": "The ending index (exclusive)", "start": "The starting index (inclusive)"}, "returns": {"peerIds": "Array of peer IDs sorted by number of votes (descending)", "voteCounts": "Array of corresponding vote counts"}}, "winnerLeaderboard(uint256,uint256)": {"details": "Gets a slice of the leaderboard", "params": {"end": "The ending index (exclusive)", "start": "The starting index (inclusive)"}, "returns": {"peerIds": "Array of peer IDs sorted by number of wins (descending)", "wins": "Array of corresponding win counts"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"addBootnodes(string[])": {"notice": "Only callable by the bootnode manager"}, "clearBootnodes()": {"notice": "Only callable by the bootnode manager"}, "grantRole(bytes32,address)": {"notice": "Only callable by the contract owner"}, "removeBootnode(uint256)": {"notice": "Only callable by the bootnode manager"}, "revokeRole(bytes32,address)": {"notice": "Only callable by the contract owner"}, "updateStageAndRound()": {"notice": "Only callable by the stage manager"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 1000000}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/SwarmCoordinator.sol": "SwarmCoordinator"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b", "urls": ["bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609", "dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/UUPSUpgradeable.sol": {"keccak256": "0xd861907d1168dcaec2a7846edbaed12feb8bad2d6781dba987be01374f90b495", "urls": ["bzz-raw://12ff809243040419e2fc2aa7ef0aaa60b3e6ebc901553ba1de970ceeef208c4c", "dweb:/ipfs/QmX2dwMVNrQAahqVzEx94gqcVB6Z8ovifPYdEfHZzj7aEb"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486", "urls": ["bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d", "dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol": {"keccak256": "0xc42facb5094f2f35f066a7155bda23545e39a3156faef3ddc00185544443ba7d", "urls": ["bzz-raw://d3b36282ab029b46bd082619a308a2ea11c309967b9425b7b7a6eb0b0c1c3196", "dweb:/ipfs/QmP2YVfDB2FoREax3vJu7QhDnyYRMw52WPrCD4vdT2kuDA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898", "urls": ["bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70", "dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c", "urls": ["bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e", "dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "src/SwarmCoordinator.sol": {"keccak256": "0xf7243562501740ec8d08dfa3a4cb635125515debdcc4f8afd36e574d650dca36", "urls": ["bzz-raw://0e2e9681fd012233f6898e84e9fe6cb999cefdd4e8f742d46333e03d40c5c1bb", "dweb:/ipfs/QmWHepH2CNbBakxHKYWHiLemb65GciVxkTk4eaebbcz5RW"], "license": "MIT"}}, "version": 1}, "id": 34}