@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

/* Reset and global styles */
body {
  margin: 0;
  padding: 0;
  font-family: "AuxMono", sans-serif;
  background-color: #2A0D04;
  color: #F0CBC5;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

/* Centering container */
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* Card styling */
.card {
  background-color: #FAD7D1;
  color: #3b1f1f;
  padding: 2rem;
  border-radius: 0px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
}

/* Title styling */
.card h1 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #3b1f1f;
  padding-bottom: 0.5rem;
}

/* Paragraph styling */
.card p {
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

/* Button styling */
.card button {
  background-color: #F0CBC5;
  color: #2A0D04;
  border: 0.05rem dashed #3b1f1f;
  padding: 0.5rem 1rem;
  border-radius: 0px;
  cursor: pointer;
  font-size: 1rem;
}

.card button:hover {
  background-color: #e2bfb9;
}

/* Footer styling */
.card footer {
  margin-top: 1.5rem;
  font-size: 0.875rem;
  color: #3b1f1f;
}
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
@font-face {
  font-family: "AuxMono";
  src: url("/fonts/Aux Mono.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}