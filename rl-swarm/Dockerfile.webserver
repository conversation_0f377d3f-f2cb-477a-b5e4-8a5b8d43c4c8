FROM python:3.11 AS server

# Port the web server runs on
ENV SWARM_UI_PORT=8000

# List of peers to pair with (e.g., "/ip4/127.0.0.1/tcp/8001,/ip4/127.0.0.1/tcp/8002")
ENV INITIAL_PEERS=""

WORKDIR /app

COPY ./web/requirements.txt .

# Install wheel and upgrade pip first
RUN pip install --upgrade pip wheel setuptools
RUN pip install --no-cache-dir -r requirements.txt

RUN opentelemetry-bootstrap -a install

# Seems to be necessary with hivemind.
RUN pip install --upgrade protobuf

# OpenTelemetry environment variables
ENV OTEL_SERVICE_NAME=""
ENV OTEL_EXPORTER_OTLP_ENDPOINT=""
ENV OTEL_PYTHON_LOG_LEVEL=info

COPY ./web/api ./api

# Copy all necessary hivemind files
COPY ./hivemind_exp/*.py ./hivemind_exp/
COPY ./hivemind_exp/contracts/*.json ./hivemind_exp/contracts/

RUN pip install pytest pytest-cov

# Run tests first, then start the server
CMD ["sh", "-c", "pytest api -v && opentelemetry-instrument python -m api.server"]