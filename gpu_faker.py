#!/usr/bin/env python3
"""
GPU伪装工具
伪装成RTX 3090 GPU以获得中等模型任务
"""

import os
import shutil
from pathlib import Path

class GPUFaker:
    """GPU伪装器"""

    def __init__(self):
        self.original_file = Path("rl-swarm/rgym_exp/src/utils/omega_gpu_resolver.py")
        self.backup_file = Path("rl-swarm/rgym_exp/src/utils/omega_gpu_resolver.py.backup")
        self.is_faked = False

    def backup_original(self):
        """备份原始文件"""
        if self.original_file.exists() and not self.backup_file.exists():
            shutil.copy2(self.original_file, self.backup_file)
            print(f"✅ 已备份原始文件: {self.backup_file}")

    def apply_rtx3090_fake(self):
        """应用RTX 3090伪装"""
        print("🎭 应用RTX 3090 GPU伪装...")

        # 备份原始文件
        self.backup_original()

        # 创建伪装版本
        fake_content = '''import random

import torch
from omegaconf import OmegaConf


def get_gpu_vram():
    """Returns the total VRAM of the first available GPU in GiB."""
    # === RTX3090_FAKE_PATCH_START ===
    # 伪装成RTX 3090 GPU (24GB VRAM)
    if os.getenv('FAKE_RTX3090_GPU', 'false').lower() == 'true':
        print("[GPU_FAKE] 🎭 伪装成RTX 3090 GPU (24GB VRAM)")
        return 24.0
    # === RTX3090_FAKE_PATCH_END ===

    if not torch.cuda.is_available():
        return 0

    total_memory = torch.cuda.get_device_properties(0).total_memory
    return total_memory / (1024**3)  # Convert bytes to GiB


def gpu_model_choice_resolver(large_model_pool, small_model_pool):
    """Selects a model from the large or small pool based on VRAM."""
    vram = get_gpu_vram()

    # === RTX3090_FAKE_PATCH_START ===
    # RTX 3090 24GB适合大模型池
    if os.getenv('FAKE_RTX3090_GPU', 'false').lower() == 'true':
        print(f"[GPU_FAKE] 🎯 检测到{vram}GB VRAM，选择大模型池")
        model_pool = large_model_pool
        selected_model = random.choice(model_pool)
        print(f"[GPU_FAKE] 🤖 选择模型: {selected_model}")
        return selected_model
    # === RTX3090_FAKE_PATCH_END ===

    if vram >= 20:  # RTX 3090门槛保持20GB
        model_pool = large_model_pool
    else:
        model_pool = small_model_pool
    return random.choice(model_pool)


# 添加os导入
import os

OmegaConf.register_new_resolver("gpu_model_choice", gpu_model_choice_resolver)
'''

        # 写入伪装版本
        with open(self.original_file, 'w') as f:
            f.write(fake_content)

        self.is_faked = True
        print("✅ RTX 3090伪装已应用")
        print("   - VRAM: 24GB")
        print("   - 模型池: 大模型池")
        print("   - 触发条件: FAKE_RTX3090_GPU=true")

    def restore_original(self):
        """恢复原始文件"""
        if self.backup_file.exists():
            shutil.copy2(self.backup_file, self.original_file)
            print("✅ 已恢复原始GPU检测")
            self.is_faked = False
        else:
            print("❌ 未找到备份文件")

    def check_status(self):
        """检查伪装状态"""
        print("=== GPU伪装状态 ===")

        if self.backup_file.exists():
            print("✅ 备份文件存在")
        else:
            print("❌ 备份文件不存在")

        # 检查当前文件是否包含伪装代码
        if self.original_file.exists():
            with open(self.original_file, 'r') as f:
                content = f.read()

            if 'RTX3090_FAKE_PATCH' in content:
                print("✅ RTX 3090伪装已激活")
                self.is_faked = True
            else:
                print("❌ RTX 3090伪装未激活")
                self.is_faked = False

        # 检查环境变量
        fake_env = os.getenv('FAKE_RTX3090_GPU', 'false')
        print(f"🔧 环境变量 FAKE_RTX3090_GPU: {fake_env}")

        return self.is_faked
        
    def cleanup(self):
        """清理备份文件"""
        if self.backup_file.exists():
            self.backup_file.unlink()
            print("🗑️  已删除备份文件")


def main():
    """主函数"""
    faker = GPUFaker()

    print("🎭 GPU伪装工具")
    print("可以伪装成RTX 3090 GPU以获得大模型任务\n")

    # 检查当前状态
    faker.check_status()

    print("\n选择操作:")
    print("1. 应用RTX 3090伪装")
    print("2. 恢复原始设置")
    print("3. 检查状态")
    print("4. 清理备份")
    print("5. 退出")

    while True:
        choice = input("\n请选择 (1-5): ").strip()

        if choice == '1':
            faker.apply_rtx3090_fake()
            break
        elif choice == '2':
            faker.restore_original()
            break
        elif choice == '3':
            faker.check_status()
        elif choice == '4':
            faker.cleanup()
            break
        elif choice == '5':
            print("👋 退出")
            break
        else:
            print("❌ 无效选择，请输入1-5")


if __name__ == "__main__":
    main()
