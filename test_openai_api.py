#!/usr/bin/env python3
"""
OpenAI API测试脚本
用于诊断API Key和连接问题
"""

import requests
import json
import os

def test_openai_api():
    """测试OpenAI API连接和权限"""
    
    # API Key
    # api_key = "********************************************************************************************************************************************************************"
    api_key = "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"

    print("🔍 OpenAI API诊断测试")
    print("=" * 50)
    
    # 测试1: 检查API Key格式
    print("\n1️⃣ 检查API Key格式...")
    if api_key.startswith("sk-"):
        print("✅ API Key格式正确")
    else:
        print("❌ API Key格式错误")
        return
    
    # 测试2: 检查账户信息
    print("\n2️⃣ 检查账户信息...")
    try:
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        # 获取账户信息
        response = requests.get(
            'https://api.openai.com/v1/models',
            headers=headers,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API Key有效，账户可访问")
            models = response.json()
            print(f"可用模型数量: {len(models.get('data', []))}")
            
            # 检查gpt-3.5-turbo是否可用
            available_models = [model['id'] for model in models.get('data', [])]
            if 'gpt-3.5-turbo' in available_models:
                print("✅ gpt-3.5-turbo 可用")
            else:
                print("❌ gpt-3.5-turbo 不可用")
                print("可用模型:", available_models[:5])
                
        elif response.status_code == 401:
            print("❌ API Key无效或未授权")
            print("错误信息:", response.text)
            
        elif response.status_code == 403:
            print("❌ 访问被禁止 (403)")
            print("可能原因:")
            print("  - 账户余额不足")
            print("  - API Key权限不足")
            print("  - 地区访问限制")
            print("错误信息:", response.text)
            
        elif response.status_code == 429:
            print("❌ 请求频率限制 (429)")
            print("错误信息:", response.text)
            
        else:
            print(f"❌ 未知错误 ({response.status_code})")
            print("错误信息:", response.text)
            
    except Exception as e:
        print(f"❌ 网络连接失败: {e}")
    
    # 测试3: 尝试简单的API调用
    print("\n3️⃣ 测试简单API调用...")
    try:
        data = {
            'model': 'gpt-3.5-turbo',
            'messages': [
                {'role': 'user', 'content': 'Hello, this is a test. Please respond with just "OK".'}
            ],
            'max_tokens': 10,
            'temperature': 0
        }
        
        response = requests.post(
            'https://api.openai.com/v1/chat/completions',
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            answer = result['choices'][0]['message']['content']
            print(f"✅ API调用成功")
            print(f"回答: {answer}")
            
            # 检查使用情况
            usage = result.get('usage', {})
            print(f"Token使用: {usage}")
            
        else:
            print(f"❌ API调用失败 ({response.status_code})")
            print("错误信息:", response.text)
            
    except Exception as e:
        print(f"❌ API调用异常: {e}")
    
    # 测试4: 检查余额 (如果有权限)
    print("\n4️⃣ 检查账户余额...")
    try:
        response = requests.get(
            'https://api.openai.com/v1/usage',
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            usage_data = response.json()
            print("✅ 余额信息获取成功")
            print(f"使用情况: {usage_data}")
        else:
            print(f"⚠️ 无法获取余额信息 ({response.status_code})")
            
    except Exception as e:
        print(f"⚠️ 余额检查失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 诊断完成")

if __name__ == "__main__":
    test_openai_api()
