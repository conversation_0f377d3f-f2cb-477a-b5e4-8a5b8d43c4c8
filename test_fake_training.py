#!/usr/bin/env python3
"""
测试伪装训练功能
验证推理攻击和训练伪装都正常工作
"""

import os
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.append('rl-swarm')

def test_stealth_attack_import():
    """测试隐蔽攻击模块导入"""
    print("🔍 测试隐蔽攻击模块导入...")
    
    # 设置环境变量
    os.environ['ENABLE_STEALTH_ATTACK'] = 'true'
    
    try:
        from rgym_exp.src.trainer import GRPOTrainerModule, _stealth_attacker, _fake_trainer
        print("✅ 隐蔽攻击模块导入成功")
        
        if _stealth_attacker:
            print("✅ 推理攻击器已激活")
        else:
            print("⚠️  推理攻击器未激活")
            
        if _fake_trainer:
            print("✅ 伪装训练器已激活")
        else:
            print("⚠️  伪装训练器未激活")
            
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_fake_training_functionality():
    """测试伪装训练功能"""
    print("\n🎭 测试伪装训练功能...")
    
    try:
        from rgym_exp.src.trainer import _fake_trainer
        
        if not _fake_trainer:
            print("❌ 伪装训练器未激活")
            return False
        
        # 创建模拟状态
        class MockState:
            def __init__(self):
                self.round = 5
                self.stage = 0
                self.peer_id = "test_peer"
        
        state = MockState()
        
        # 测试伪装训练
        print("🚀 执行伪装训练测试...")
        start_time = time.time()
        
        result = _fake_trainer.fake_train_step(state, None, None)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result:
            print(f"✅ 伪装训练成功")
            print(f"📊 训练时长: {duration:.1f}s")
            print(f"🏆 伪装奖励: {result.get('reward', 'N/A')}")
            print(f"📉 伪装损失: {result.get('loss', 'N/A')}")
            return True
        else:
            print("❌ 伪装训练失败")
            return False
            
    except Exception as e:
        print(f"❌ 伪装训练测试失败: {e}")
        return False

def test_trainer_integration():
    """测试训练器集成"""
    print("\n🔧 测试训练器集成...")
    
    try:
        # 模拟创建训练器（不实际加载模型）
        print("📦 模拟训练器创建...")
        
        # 这里我们只测试类的创建，不实际初始化模型
        from rgym_exp.src.trainer import GRPOTrainerModule
        
        # 检查方法是否存在
        methods_to_check = ['train', 'step', 'compute_loss', 'training_step']
        
        for method in methods_to_check:
            if hasattr(GRPOTrainerModule, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
                return False
        
        print("✅ 训练器集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 训练器集成测试失败: {e}")
        return False

def test_environment_variables():
    """测试环境变量控制"""
    print("\n🌍 测试环境变量控制...")
    
    # 测试关闭状态
    os.environ['ENABLE_STEALTH_ATTACK'] = 'false'
    
    try:
        # 重新导入以测试环境变量效果
        import importlib
        import rgym_exp.src.trainer
        importlib.reload(rgym_exp.src.trainer)
        
        from rgym_exp.src.trainer import _fake_trainer
        
        if _fake_trainer is None:
            print("✅ 环境变量控制正常 - 关闭状态")
        else:
            print("⚠️  环境变量控制异常 - 应该关闭但仍激活")
        
        # 重新开启
        os.environ['ENABLE_STEALTH_ATTACK'] = 'true'
        importlib.reload(rgym_exp.src.trainer)
        
        from rgym_exp.src.trainer import _fake_trainer
        
        if _fake_trainer is not None:
            print("✅ 环境变量控制正常 - 开启状态")
            return True
        else:
            print("⚠️  环境变量控制异常 - 应该开启但未激活")
            return False
            
    except Exception as e:
        print(f"❌ 环境变量测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始伪装训练功能测试")
    print("=" * 50)
    
    tests = [
        test_stealth_attack_import,
        test_fake_training_functionality,
        test_trainer_integration,
        test_environment_variables
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            time.sleep(1)  # 短暂延迟
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！伪装训练功能正常")
        print("✅ 推理攻击功能保持完整")
        print("✅ 训练伪装功能正常工作")
        print("✅ 环境变量控制正常")
    else:
        print("⚠️  部分测试失败，请检查实现")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
