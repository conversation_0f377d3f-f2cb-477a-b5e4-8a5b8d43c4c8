#!/usr/bin/env python3
"""
无模型推理攻击启动器
1. 取消真实模型推理
2. 增加详细日志输出
3. 显示上链信息
4. 实时攻击统计
5. 支持RTX 3090 GPU伪装
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path

def ask_rtx3090_fake():
    """询问是否启用完整RTX 3090伪装"""
    print("\n🎭 完整RTX 3090伪装选项")
    print("是否要完全伪装成RTX 3090工作站以获得大模型任务？")
    print("  ✅ 优势: 可能获得更复杂任务和更高奖励")
    print("  ✅ 完整: 伪装GPU、CPU、内存、系统信息")
    print("  ✅ 合理: RTX 3090是经典的高端消费级显卡")
    print("  📊 当前: CPU模式 (小模型)")
    print("  🎯 伪装: RTX 3090工作站 (大模型)")
    print("  🖥️  包含: GPU、CPU、内存、系统全面伪装")
    print("\n✅ 自动启用完整RTX 3090伪装")
    return True

def apply_rtx3090_fake():
    """应用完整RTX 3090伪装"""
    print("\n🎭 应用完整RTX 3090伪装...")

    try:
        # 导入完整RTX 3090伪装工具
        sys.path.append('.')
        from complete_rtx3090_faker import enable_complete_rtx3090_fake
        from gpu_faker import GPUFaker

        # 应用GPU选择伪装
        gpu_faker = GPUFaker()
        gpu_faker.apply_rtx3090_fake()

        # 应用完整系统伪装
        complete_faker = enable_complete_rtx3090_fake()

        # 设置环境变量
        os.environ['FAKE_RTX3090_GPU'] = 'true'
        print("✅ 完整RTX 3090伪装已应用")
        print("   🖥️  GPU: NVIDIA GeForce RTX 3090")
        print("   💾 VRAM: 24GB")
        print("   🧠 CPU: Intel i7-12700K 12-Core")
        print("   📊 RAM: 32GB")
        print("   🔧 系统: Ubuntu 22.04 LTS")

        return True

    except Exception as e:
        print(f"❌ 完整RTX 3090伪装失败: {e}")
        return False

def apply_model_fake():
    """应用模型伪装"""
    print("\n🎭 应用模型伪装...")

    try:
        # 导入模型伪装工具
        sys.path.append('.')
        from model_faker import enable_model_faking

        # 启用模型伪装
        faker = enable_model_faking()

        # 设置环境变量
        os.environ['FAKE_MODEL_LOADING'] = 'true'
        print("✅ 模型伪装已启用")
        print("   🎯 真实模型名称: 保持不变")
        print("   💾 模型文件: 使用虚拟模型 (~50MB)")
        print("   📊 内存节省: ~95%")

        return True

    except ImportError as e:
        print(f"❌ 模型伪装失败: {e}")
        print("💡 原因: 依赖库未安装或不在虚拟环境中")
        print("💡 解决: 模型伪装将在虚拟环境激活后自动应用")
        print("📊 当前: 将使用真实模型加载")
        return False
    except Exception as e:
        print(f"❌ 模型伪装失败: {e}")
        print("📊 当前: 将使用真实模型加载")
        return False

def setup_enhanced_environment():
    """设置增强的攻击环境"""
    print("=== 设置无模型推理攻击环境 ===")
    
    # 设置OpenAI API Key
    openai_key = "********************************************************************************************************************************************************************"
    os.environ['OPENAI_API_KEY'] = openai_key
    print("✅ OpenAI API Key已设置")
    
    # 提取ORG_ID
    user_data_path = Path("rl-swarm/modal-login/temp-data/userData.json")
    if user_data_path.exists():
        with open(user_data_path, 'r') as f:
            user_data = json.load(f)
        
        for user_id, user_info in user_data.items():
            org_id = user_info.get('orgId')
            if org_id:
                os.environ['ORG_ID'] = org_id
                print(f"✅ ORG_ID已设置: {org_id}")
                break
    
    # 设置身份文件路径 (修复peer身份变化问题)
    identity_path = os.path.join(os.getcwd(), "rl-swarm", "swarm.pem")
    os.environ['IDENTITY_PATH'] = identity_path
    print(f"✅ 身份文件路径已设置: {identity_path}")

    # 设置其他环境变量
    os.environ['SWARM_CONTRACT'] = '0xFaD7C5e93f28257429569B854151A1B8DCD404c2'
    os.environ['ENABLE_STEALTH_ATTACK'] = 'true'

    # 检查是否需要延迟应用模型伪装
    if os.getenv('FAKE_MODEL_LOADING') == 'true':
        print("🎭 模型伪装将在虚拟环境激活后应用")

    print("✅ 所有环境变量已设置")

def apply_enhanced_patch():
    """应用增强的攻击补丁"""
    print("\n=== 应用增强攻击补丁 ===")
    
    try:
        # 重新应用补丁
        result = subprocess.run(["python3", "precise_attack_patch.py"], 
                              capture_output=True, text=True, check=True)
        print("✅ 增强攻击补丁应用成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 攻击补丁应用失败: {e}")
        return False

def verify_enhanced_setup():
    """验证增强攻击设置"""
    print("\n=== 验证增强攻击设置 ===")
    
    checks = []
    
    # 检查实时计算引擎
    try:
        # 测试实时计算功能
        test_expr = "123 + 456"
        result = eval(test_expr)
        print(f"✅ 实时计算引擎: 可用 (测试: {test_expr} = {result})")
        checks.append(True)
    except Exception as e:
        print(f"❌ 实时计算引擎: 失败 ({e})")
        checks.append(False)
    
    # 检查攻击补丁
    trainer_path = Path("rl-swarm/rgym_exp/src/trainer.py")
    if trainer_path.exists():
        with open(trainer_path, 'r') as f:
            content = f.read()
        if "STEALTH_ATTACK_PATCH" in content:
            print("✅ 攻击补丁已应用")
            checks.append(True)
        else:
            print("❌ 攻击补丁未应用")
            checks.append(False)
    else:
        print("❌ trainer.py不存在")
        checks.append(False)
    
    # 检查区块链日志增强
    chain_utils_path = Path("rl-swarm/hivemind_exp/chain_utils.py")
    if chain_utils_path.exists():
        with open(chain_utils_path, 'r') as f:
            content = f.read()
        if "[BLOCKCHAIN]" in content:
            print("✅ 区块链日志增强已应用")
            checks.append(True)
        else:
            print("❌ 区块链日志增强未应用")
            checks.append(False)
    else:
        print("❌ chain_utils.py不存在")
        checks.append(False)
    
    return all(checks)

def show_enhanced_attack_info():
    """显示增强攻击信息"""
    print("\n=== 完整隐蔽攻击信息 ===")

    print("🎯 攻击策略 (无真实模型推理 + 伪装训练):")
    print("   1️⃣ 实时计算引擎 (60%) - 数学问题本地计算+推理生成")
    print("   2️⃣ OpenRouter API (40%) - 复杂问题标准格式推理")
    print("   3️⃣ 智能回退机制 - 多层保障确保100%覆盖")
    print("   4️⃣ 错误注入系统 - 真实错误模式模拟")
    print("   5️⃣ 伪装训练 - 完全跳过模型训练但保持区块链交互")

    print("\n📊 预期效果:")
    print("   - 100% 避免GPU推理 (节省计算资源)")
    print("   - 100% 避免模型训练 (节省大量计算资源)")
    print("   - 100% 问题覆盖率 (双引擎+回退机制)")
    print("   - 标准推理格式 (完全隐蔽)")
    print("   - 更快启动速度 (无预计算文件加载)")

    print("\n🎭 伪装训练特性:")
    print("   - RTX 3090级别的假训练时间 (15-35秒)")
    print("   - RTX 3090级别的假奖励 (0.60-0.80)")
    print("   - 逼真的训练日志输出")
    print("   - 正常的区块链奖励提交")
    print("   - 完全跳过梯度计算和参数更新")

    print("\n🔗 区块链日志增强:")
    print("   - 详细的上链信息")
    print("   - API调用跟踪")
    print("   - 交易状态监控")
    print("   - Gas费用信息")

    print("\n📈 实时统计:")
    print("   - 问题处理计数")
    print("   - 策略使用分布")
    print("   - 攻击成功率")
    print("   - 错误注入统计")
    print("   - 伪装训练统计")

def launch_enhanced_attack():
    """启动增强攻击"""
    print("\n=== 启动完整隐蔽攻击 ===")

    # 切换到RL Swarm目录
    os.chdir("rl-swarm")

    max_retries = 10
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            print(f"🎯 启动完整隐蔽攻击 (推理 + 训练)... (尝试 {retry_count + 1}/{max_retries})")
            print("📊 实时监控 (详细日志模式):")
            print("=" * 80)
            
            # 启动攻击 (使用与run_rl_swarm.sh相同的配置路径)
            current_dir = os.getcwd()
            config_path = os.path.join(current_dir, "rgym_exp", "config")

            print(f"🔧 当前工作目录: {current_dir}")
            print(f"🔧 配置文件路径: {config_path}")
            print(f"🔧 配置文件存在: {os.path.exists(os.path.join(config_path, 'rg-swarm.yaml'))}")

            cmd = [
                "bash", "-c",
                f"source .venv/bin/activate && python -m rgym_exp.runner.swarm_launcher --config-path {config_path} --config-name rg-swarm.yaml"
            ]
            
            # 确保环境变量传递给子进程
            env = os.environ.copy()
            env['IDENTITY_PATH'] = os.path.join(os.path.dirname(os.getcwd()), "rl-swarm", "swarm.pem")
            env['ENABLE_STEALTH_ATTACK'] = 'true'
            env['SWARM_CONTRACT'] = '0xFaD7C5e93f28257429569B854151A1B8DCD404c2'

            if 'ORG_ID' in os.environ:
                env['ORG_ID'] = os.environ['ORG_ID']
            if 'OPENAI_API_KEY' in os.environ:
                env['OPENAI_API_KEY'] = os.environ['OPENAI_API_KEY']
            if os.getenv('FAKE_MODEL_LOADING') == 'true':
                env['FAKE_MODEL_LOADING'] = 'true'
                print("🎭 模型伪装环境变量已传递到子进程")
            if os.getenv('FAKE_RTX3090_GPU') == 'true':
                env['FAKE_RTX3090_GPU'] = 'true'
                print("🎭 RTX 3090伪装环境变量已传递到子进程")

            print(f"🔧 子进程环境变量:")
            print(f"   IDENTITY_PATH: {env.get('IDENTITY_PATH')}")
            print(f"   ORG_ID: {env.get('ORG_ID', 'Not set')}")
            print(f"   ENABLE_STEALTH_ATTACK: {env.get('ENABLE_STEALTH_ATTACK')}")
            print(f"   FAKE_MODEL_LOADING: {env.get('FAKE_MODEL_LOADING', 'false')}")
            print(f"   FAKE_RTX3090_GPU: {env.get('FAKE_RTX3090_GPU', 'false')}")

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                env=env
            )
            
            # 监控关键指标
            attack_status = {
                'answers_loaded': False,
                'attack_activated': False,
                'fake_training_activated': False,
                'training_started': False,
                'connected': False,
                'peer_registered': False,
                'blockchain_calls': 0,
                'questions_processed': 0,
                'fake_training_steps': 0
            }
            
            last_output_time = time.time()
            
            while True:
                line = process.stdout.readline()
                if not line:
                    # 检查进程是否还在运行
                    if process.poll() is not None:
                        break
                    
                    # 检查是否长时间无输出 (可能卡死)
                    if time.time() - last_output_time > 300:  # 5分钟无输出
                        print("⚠️  检测到长时间无输出，可能进程卡死")
                        process.terminate()
                        time.sleep(5)
                        if process.poll() is None:
                            process.kill()
                        break
                    
                    time.sleep(1)
                    continue
                
                last_output_time = time.time()
                
                # 显示输出
                print(line.rstrip())
                
                # 检查DHT错误
                error_patterns = [
                    "Resource temporarily unavailable",
                    "EOFError",
                    "hivemind.dht",
                    "Connection refused",
                    "Broken pipe",
                    "Connection reset by peer",
                    "TimeoutError",
                    "OSError: [Errno 32]",
                    "BlockingIOError"
                ]

                if any(pattern in line for pattern in error_patterns):
                    print(f"🚨 检测到系统错误: {line.strip()}")
                    print("🔄 准备重启进程...")
                    process.terminate()
                    time.sleep(5)
                    if process.poll() is None:
                        process.kill()
                    raise Exception(f"System error detected: {line.strip()}")
                
                # 统计关键事件
                if "[STEALTH]" in line:
                    if "实时计算引擎" in line or "OpenRouter API" in line:
                        attack_status['answers_loaded'] = True
                        print("🎯 ✅ 双引擎系统已激活")
                    elif "隐蔽攻击已激活" in line:
                        attack_status['attack_activated'] = True
                        print("🎯 ✅ 攻击已激活")
                    elif "伪装训练模块已激活" in line or "训练器伪装模式已启用" in line:
                        attack_status['fake_training_activated'] = True
                        print("🎭 ✅ 伪装训练已激活")
                    elif "伪装训练完成" in line:
                        attack_status['fake_training_steps'] += 1
                        print(f"🎭 📊 伪装训练步骤: {attack_status['fake_training_steps']}")
                    elif "处理问题" in line:
                        attack_status['questions_processed'] += 1
                        if attack_status['questions_processed'] % 5 == 0:
                            print(f"🎯 📊 已处理 {attack_status['questions_processed']} 个问题")
                
                # 统计区块链调用
                if "[BLOCKCHAIN]" in line:
                    attack_status['blockchain_calls'] += 1
                    if "API调用" in line:
                        print(f"🔗 📊 区块链调用次数: {attack_status['blockchain_calls']}")
                
                # 检查连接状态
                if "Connected to Gensyn Testnet" in line:
                    attack_status['connected'] = True
                    print("🌐 ✅ 已连接到Gensyn测试网")
                
                # 检查训练状态
                if any(keyword in line for keyword in ["Training started", "Episode", "Round", "Step"]):
                    if not attack_status['training_started']:
                        attack_status['training_started'] = True
                        print("🚀 ✅ 训练已开始，完整隐蔽攻击正在进行!")
            
            # 等待进程结束
            exit_code = process.wait()
            
            print("=" * 80)
            print(f"进程结束，退出码: {exit_code}")
            
            # 显示最终统计
            print("\n📊 完整隐蔽攻击统计:")
            print(f"   双引擎系统: {'✅' if attack_status['answers_loaded'] else '❌'}")
            print(f"   推理攻击激活: {'✅' if attack_status['attack_activated'] else '❌'}")
            print(f"   伪装训练激活: {'✅' if attack_status['fake_training_activated'] else '❌'}")
            print(f"   网络连接: {'✅' if attack_status['connected'] else '❌'}")
            print(f"   训练开始: {'✅' if attack_status['training_started'] else '❌'}")
            print(f"   问题处理: {attack_status['questions_processed']} 个")
            print(f"   伪装训练步骤: {attack_status['fake_training_steps']} 次")
            print(f"   区块链调用: {attack_status['blockchain_calls']} 次")

            # 判断成功
            success = (attack_status['answers_loaded'] and
                      attack_status['attack_activated'] and
                      attack_status['fake_training_activated'])

            if success:
                print("🎉 完整隐蔽攻击成功激活!")
                print("   ✅ 推理攻击已激活")
                print("   ✅ 训练伪装已激活")
                return True
            else:
                print("⚠️  隐蔽攻击部分激活:")
                print(f"   推理攻击: {'✅' if attack_status['attack_activated'] else '❌'}")
                print(f"   训练伪装: {'✅' if attack_status['fake_training_activated'] else '❌'}")
                print(f"   双引擎系统: {'✅' if attack_status['answers_loaded'] else '❌'}")
                
                # 如果是正常退出但功能未完全激活，也算成功
                if exit_code == 0:
                    return True
                    
                # 异常退出，准备重试
                raise Exception(f"Process exited with code {exit_code}")
            
        except KeyboardInterrupt:
            print("\n⏹️  用户中断攻击")
            return False
        except Exception as e:
            retry_count += 1
            print(f"\n❌ 攻击启动异常 (尝试 {retry_count}/{max_retries}): {e}")
            
            if retry_count < max_retries:
                wait_time = min(30, 10 * retry_count)  # 递增等待时间
                print(f"🔄 等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
                
                # 清理可能残留的进程
                try:
                    subprocess.run(["pkill", "-f", "swarm_launcher"], capture_output=True)
                    subprocess.run(["pkill", "-f", "hivemind"], capture_output=True)
                    time.sleep(2)
                except:
                    pass
                    
                print(f"🔄 准备第 {retry_count + 1} 次重试...")
            else:
                print("❌ 达到最大重试次数，攻击启动失败!")
                return False
    
    return False

def main():
    """主函数"""
    print("🚀 无模型推理RL Swarm隐蔽攻击启动器")
    print("🎯 取消真实模型推理 + 详细日志")
    print("🔗 区块链上链信息监控")
    print("🎭 支持RTX 3090 GPU伪装")
    print("⚠️  警告: 仅用于安全研究目的!\n")

    # RTX 3090伪装选择 - 自动启用
    use_rtx3090_fake = ask_rtx3090_fake()

    if use_rtx3090_fake:
        print("\n🎭 启用RTX 3090伪装模式")
        if not apply_rtx3090_fake():
            print("❌ RTX 3090伪装失败，继续使用小模型")
            use_rtx3090_fake = False
    else:
        print("\n📱 使用标准模式 (小模型)")

    # 模型伪装 - 自动启用
    print("\n🎭 模型伪装选项")
    print("是否要使用虚拟模型以节省内存？")
    print("  ✅ 优势: 内存从8GB降到2GB，启动更快")
    print("  ✅ 安全: 保持真实模型名称，服务端无法检测")
    print("  ⚠️  注意: 推理完全依赖预计算答案和ChatGPT")
    print("\n✅ 自动启用模型伪装")
    
    if not apply_model_fake():
        print("❌ 模型伪装失败，继续使用真实模型")

    # 设置环境
    setup_enhanced_environment()

    # 应用增强补丁
    if not apply_enhanced_patch():
        print("\n❌ 增强补丁应用失败!")
        return False

    # 验证设置
    if not verify_enhanced_setup():
        print("\n❌ 增强攻击设置验证失败!")
        return False

    print("\n✅ 增强攻击设置验证通过!")

    # 显示攻击信息
    show_enhanced_attack_info()

    # 显示GPU模式
    if use_rtx3090_fake:
        print("\n🎭 完整RTX 3090伪装状态:")
        print("   🖥️  GPU: NVIDIA GeForce RTX 3090 (24GB VRAM)")
        print("   🧠 CPU: Intel i7-12700K 12-Core (24线程)")
        print("   📊 RAM: 32GB DDR4")
        print("   🔧 系统: Ubuntu 22.04 LTS")
        print("   🤖 模型: 大模型池 (1.5B-1.7B参数)")
        print("   🎯 任务: 可能获得更复杂任务")
        print("   🛡️  检测: 全面硬件信息伪装")
    else:
        print("\n📱 标准模式状态:")
        print("   💻 真实: CPU模式 (0GB VRAM)")
        print("   🤖 模型: 小模型池 (0.5B-0.6B参数)")
        print("   🎯 任务: 标准数学问题")
        print("   📊 硬件: 真实系统信息")
    
    # 自动确认启动
    print("\n🤔 确认启动完整隐蔽攻击?")
    print("这将:")
    print("  ✅ 完全避免真实模型推理")
    print("  ✅ 完全避免真实模型训练")
    print("  ✅ 显示详细的上链日志")
    print("  ✅ 实时攻击统计")
    print("  ✅ 100% 隐蔽操作")
    print("  ✅ RTX 3090级别的伪装性能")
    print("\n✅ 自动确认启动")
    
    # 启动增强攻击
    print("\n🚀 启动完整隐蔽攻击...")
    success = launch_enhanced_attack()

    if success:
        print("\n🎉 完整隐蔽攻击成功部署!")
        print("📊 监控 [STEALTH] 和 [BLOCKCHAIN] 标记")
        print("🎭 推理攻击 + 训练伪装 双重隐蔽")
    else:
        print("\n❌ 完整隐蔽攻击部署失败!")
    
    return success

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  攻击被用户中断")
    except Exception as e:
        print(f"\n💥 攻击执行出错: {e}")
    finally:
        print("\n🧹 清理完成")
