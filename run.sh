#!/bin/bash

set -euo pipefail

# General arguments
ROOT=$PWD

# Python version to install
PYTHON_VERSION="3.12"

# Check for Python 3.12
if ! command -v python$PYTHON_VERSION &> /dev/null; then
    echo "Python $PYTHON_VERSION not found. Installing Python $PYTHON_VERSION..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        brew install python@$PYTHON_VERSION
    elif grep -qi "ubuntu\|debian" /etc/os-release 2> /dev/null || uname -r | grep -qi "microsoft"; then
        # Ubuntu/Debian/WSL
        sudo apt update
        sudo apt install -y python$PYTHON_VERSION python$PYTHON_VERSION-dev python$PYTHON_VERSION-venv python3-pip python3-full build-essential libssl-dev libffi-dev
    elif grep -qi "fedora\|rhel\|centos" /etc/os-release 2> /dev/null; then
        # Fedora/RHEL/CentOS
        sudo dnf install -y python$PYTHON_VERSION python$PYTHON_VERSION-devel python$PYTHON_VERSION-venv python3-pip gcc gcc-c++ make
    else
        echo "Unsupported OS for automatic Python installation. Please install Python $PYTHON_VERSION manually."
        exit 1
    fi
else
    # Python is installed but we might still need dev packages
    if grep -qi "ubuntu\|debian" /etc/os-release 2> /dev/null || uname -r | grep -qi "microsoft"; then
        echo "Ensuring Python development headers and build dependencies are installed..."
        sudo apt update
        sudo apt install -y python$PYTHON_VERSION-dev python$PYTHON_VERSION-venv build-essential libssl-dev libffi-dev
    fi
fi

# Create a virtual environment only in the rl-swarm directory
echo "Creating virtual environment in rl-swarm directory..."
cd rl-swarm/
python$PYTHON_VERSION -m venv .venv
source .venv/bin/activate

# Now use the virtual environment's pip
PIP_CMD="pip"
$PIP_CMD install --upgrade pip
$PIP_CMD install torch transformers omegaconf psutil requests
$PIP_CMD install gensyn-genrl==0.1.4
$PIP_CMD install reasoning-gym>=0.1.20
$PIP_CMD install trl
$PIP_CMD install hivemind@git+https://github.com/gensyn-ai/hivemind@639c964a8019de63135a2594663b5bec8e5356dd

# Check for Node.js
cd modal-login
if ! command -v node > /dev/null 2>&1 || [ "$(node -v | cut -d. -f1 | tr -d 'v')" -lt 20 ]; then
    echo "Node.js not found or version < 20. Installing Node.js 20..."
    if grep -qi "ubuntu\|debian" /etc/os-release 2> /dev/null || uname -r | grep -qi "microsoft"; then
        # Ubuntu/Debian/WSL
        curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
        sudo apt install -y nodejs
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        brew install node@20
        brew link --overwrite node@20
    else
        echo "Unsupported OS for automatic Node.js installation. Please install Node.js 20.x manually."
        exit 1
    fi
fi

# Check if npm is available
if ! command -v npm > /dev/null 2>&1; then
    echo "npm not found. Please check your Node.js installation."
    exit 1
fi

# Install yarn if not available
if ! command -v yarn > /dev/null 2>&1; then
    echo "Yarn not found. Installing Yarn..."
    if grep -qi "ubuntu\|debian" /etc/os-release 2> /dev/null || uname -r | grep -qi "microsoft"; then
        # Ubuntu/Debian/WSL
        curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | sudo apt-key add -
        echo "deb https://dl.yarnpkg.com/debian/ stable main" | sudo tee /etc/apt/sources.list.d/yarn.list
        sudo apt update && sudo apt install -y yarn
    else
        # Install with npm as fallback
        npm install -g yarn
    fi
fi

# Create logs directory if it doesn't exist
mkdir -p "$ROOT/rl-swarm/logs"

# Only build if needed
if [ ! -d "$ROOT/rl-swarm/modal-login/.next" ]; then
  echo "Building server..."
  yarn install --immutable
  echo "Building server"
  yarn build > "$ROOT/rl-swarm/logs/yarn.log" 2>&1
else
  echo "Server already built. Skipping build step."
  yarn install --immutable --check-files > /dev/null 2>&1
fi

yarn start >> "$ROOT/rl-swarm/logs/yarn.log" 2>&1 & # Run in background and log output

SERVER_PID=$!  # Store the process ID
echo "Started server process: $SERVER_PID"
sleep 5

# Return to the project root directory and run start.py
cd "$ROOT"
echo "Returning to project root directory: $ROOT"

# Set OpenRouter API Key for stealth attack (if enabled)
OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-"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"}
export OPENROUTER_API_KEY

echo "Running start.py with the virtual environment..."
# Ensure we're using the rl-swarm virtual environment's Python
source "$ROOT/rl-swarm/.venv/bin/activate"
python start.py

wait  # Keep script running until Ctrl+C
