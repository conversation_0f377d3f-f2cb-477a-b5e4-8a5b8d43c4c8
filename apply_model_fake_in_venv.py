#!/usr/bin/env python3
"""
在虚拟环境中应用模型伪装
这个脚本会在RL-Swarm启动时被调用
"""

import os
import sys

def apply_model_fake_in_venv():
    """在虚拟环境中应用模型伪装"""
    
    # 检查是否需要应用模型伪装
    if os.getenv('FAKE_MODEL_LOADING') != 'true':
        return
    
    print("[MODEL_FAKE] 🎭 在虚拟环境中应用模型伪装...")
    
    try:
        # 添加项目根目录到路径
        project_root = os.path.dirname(os.path.abspath(__file__))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        # 导入模型伪装工具
        from model_faker import enable_model_faking
        
        # 启用模型伪装
        faker = enable_model_faking()
        
        print("[MODEL_FAKE] ✅ 模型伪装在虚拟环境中成功应用")
        print("[MODEL_FAKE] 🎯 真实模型名称: 保持不变")
        print("[MODEL_FAKE] 💾 模型文件: 使用虚拟模型 (~50MB)")
        print("[MODEL_FAKE] 📊 内存节省: ~95%")
        
        return True
        
    except Exception as e:
        print(f"[MODEL_FAKE] ❌ 在虚拟环境中应用模型伪装失败: {e}")
        print(f"[MODEL_FAKE] 📊 将使用真实模型加载")
        return False

if __name__ == "__main__":
    apply_model_fake_in_venv()
